import React, { useState, useEffect } from "react";
import AdminLayout from "@/components/admin/AdminLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, Search, Plus, Mail, Edit, Trash2, Copy, Eye } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/hooks/use-toast";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import EnhancedEmailEditor from "@/components/admin/EnhancedEmailEditor";
import {
  getEmailTemplates,
  getEmailTemplate,
  getEmailTemplatesByCategory,
  getEmailTemplateCategories,
  createEmailTemplate,
  updateEmailTemplate,
  deleteEmailTemplate,
  clearAllEmailTemplates,
  forceClearAllEmailTemplates,
  previewEmailTemplate
} from "@/api/emailTemplates";
import { EmailTemplate, EmailTemplateCategory, EMAIL_PLACEHOLDERS } from "@/lib/email-templates";

export default function EmailTemplatesPage() {
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const [categories, setCategories] = useState<EmailTemplateCategory[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isClearAllDialogOpen, setIsClearAllDialogOpen] = useState(false);
  const [isForceClearAllDialogOpen, setIsForceClearAllDialogOpen] = useState(false);
  const [isPreviewDialogOpen, setIsPreviewDialogOpen] = useState(false);

  const [selectedTemplate, setSelectedTemplate] = useState<EmailTemplate | null>(null);
  const [previewData, setPreviewData] = useState<{
    subject: string;
    htmlContent: string;
    textContent: string;
  } | null>(null);

  const [newTemplate, setNewTemplate] = useState<Partial<EmailTemplate>>({
    name: "",
    description: "",
    subject: "",
    htmlContent: "",
    category: "",
    isDefault: false
  });

  const { toast } = useToast();

  // Fetch templates and categories on component mount
  useEffect(() => {
    fetchTemplatesAndCategories();
  }, []);

  // Filter templates by category and search query
  const filteredTemplates = templates.filter(
    (template) =>
      (activeTab === "all" || template.category === activeTab) &&
      (searchQuery === "" ||
        template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.subject.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // Fetch templates and categories
  const fetchTemplatesAndCategories = async () => {
    setIsLoading(true);
    try {
      const [templatesData, categoriesData] = await Promise.all([
        getEmailTemplates(),
        getEmailTemplateCategories()
      ]);

      setTemplates(templatesData);
      setCategories(categoriesData);
    } catch (error) {
      console.error("Error fetching email templates:", error);
      toast({
        title: "Error",
        description: "Failed to fetch email templates",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle creating a new template
  const handleCreateTemplate = async () => {
    try {
      // Generate a unique template ID
      const templateId = `template_${Date.now()}`;

      const createdTemplate = await createEmailTemplate({
        ...newTemplate,
        templateId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });

      setTemplates([...templates, createdTemplate]);
      setIsCreateDialogOpen(false);

      toast({
        title: "Success",
        description: "Email template created successfully",
      });

      // Reset new template form
      setNewTemplate({
        name: "",
        description: "",
        subject: "",
        htmlContent: "",
        category: "",
        isDefault: false
      });
    } catch (error) {
      console.error("Error creating email template:", error);
      toast({
        title: "Error",
        description: "Failed to create email template",
        variant: "destructive",
      });
    }
  };

  // Handle updating a template
  const handleUpdateTemplate = async () => {
    if (!selectedTemplate) return;

    try {
      const updatedTemplate = await updateEmailTemplate(
        selectedTemplate.id as number,
        {
          ...selectedTemplate,
          updatedAt: new Date().toISOString()
        }
      );

      setTemplates(templates.map(template =>
        template.id === updatedTemplate.id ? updatedTemplate : template
      ));

      setIsEditDialogOpen(false);

      toast({
        title: "Success",
        description: "Email template updated successfully",
      });
    } catch (error) {
      console.error("Error updating email template:", error);
      toast({
        title: "Error",
        description: "Failed to update email template",
        variant: "destructive",
      });
    }
  };

  // Handle deleting a template
  const handleDeleteTemplate = async () => {
    if (!selectedTemplate) return;

    try {
      await deleteEmailTemplate(selectedTemplate.id as number);

      setTemplates(templates.filter(template => template.id !== selectedTemplate.id));
      setIsDeleteDialogOpen(false);

      toast({
        title: "Success",
        description: "Email template deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting email template:", error);
      toast({
        title: "Error",
        description: "Failed to delete email template",
        variant: "destructive",
      });
    }
  };

  // Handle clearing all non-default templates
  const handleClearAllTemplates = async () => {
    try {
      const result = await clearAllEmailTemplates();

      // Remove all non-default templates from the state
      setTemplates(templates.filter(template => template.isDefault));
      setIsClearAllDialogOpen(false);

      toast({
        title: "Success",
        description: result.message,
      });
    } catch (error) {
      console.error("Error clearing email templates:", error);
      toast({
        title: "Error",
        description: "Failed to clear email templates",
        variant: "destructive",
      });
    }
  };

  // Handle force clearing ALL templates (including defaults)
  const handleForceClearAllTemplates = async () => {
    try {
      const result = await forceClearAllEmailTemplates();

      // Clear all templates from the state
      setTemplates([]);
      setIsForceClearAllDialogOpen(false);

      toast({
        title: "Success",
        description: result.message,
      });
    } catch (error) {
      console.error("Error force clearing email templates:", error);
      toast({
        title: "Error",
        description: "Failed to force clear email templates",
        variant: "destructive",
      });
    }
  };

  // Handle duplicating a template
  const handleDuplicateTemplate = async (template: EmailTemplate) => {
    try {
      // Generate a unique template ID
      const templateId = `template_${Date.now()}`;

      const duplicatedTemplate = await createEmailTemplate({
        ...template,
        id: undefined,
        templateId,
        name: `${template.name} (Copy)`,
        isDefault: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });

      setTemplates([...templates, duplicatedTemplate]);

      toast({
        title: "Success",
        description: "Email template duplicated successfully",
      });
    } catch (error) {
      console.error("Error duplicating email template:", error);
      toast({
        title: "Error",
        description: "Failed to duplicate email template",
        variant: "destructive",
      });
    }
  };

  // Handle previewing a template
  const handlePreviewTemplate = async (template: EmailTemplate) => {
    try {
      // Generate test data from placeholders
      const testData: Record<string, string> = {};

      EMAIL_PLACEHOLDERS.forEach(placeholder => {
        testData[placeholder.name] = placeholder.example;
      });

      // Add current year
      testData.currentYear = new Date().getFullYear().toString();

      const previewResult = await previewEmailTemplate(
        template.id as number,
        testData
      );

      setPreviewData(previewResult);
      setIsPreviewDialogOpen(true);
    } catch (error) {
      console.error("Error previewing email template:", error);
      toast({
        title: "Error",
        description: "Failed to preview email template",
        variant: "destructive",
      });
    }
  };

  return (
    <AdminLayout>
      <Card className="shadow-lg">
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="text-2xl font-bold">Email Templates</CardTitle>
              <CardDescription>
                Manage and customize email templates for your application
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setIsClearAllDialogOpen(true)}
                disabled={templates.filter(t => !t.isDefault).length === 0}
                title="Clear all non-default templates"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Clear All
              </Button>
              <Button
                variant="destructive"
                onClick={() => setIsForceClearAllDialogOpen(true)}
                disabled={templates.length === 0}
                title="Force clear ALL templates (including defaults)"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Force Clear ALL
              </Button>
              <Button onClick={() => setIsCreateDialogOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                New Template
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : (
            <div className="space-y-6">
              <div className="flex items-center gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search templates..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>

              <Tabs defaultValue={activeTab} onValueChange={setActiveTab}>
                <TabsList className="mb-4 flex flex-wrap">
                  <TabsTrigger value="all">All Categories</TabsTrigger>
                  {categories.map((category) => (
                    <TabsTrigger key={category.id} value={category.id}>
                      {category.name}
                    </TabsTrigger>
                  ))}
                </TabsList>

                <TabsContent value={activeTab} className="space-y-4">
                  {filteredTemplates.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      No templates found
                    </div>
                  ) : (
                    <div className="rounded-md border">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Name</TableHead>
                            <TableHead>Category</TableHead>
                            <TableHead>Subject</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {filteredTemplates.map((template) => (
                            <TableRow key={template.id}>
                              <TableCell>
                                <div className="font-medium">{template.name}</div>
                                <div className="text-sm text-muted-foreground">{template.description}</div>
                              </TableCell>
                              <TableCell>
                                <Badge variant="outline">{template.category}</Badge>
                                {template.isDefault && (
                                  <Badge className="ml-2 bg-primary text-primary-foreground">Default</Badge>
                                )}
                              </TableCell>
                              <TableCell className="max-w-[300px] truncate">{template.subject}</TableCell>
                              <TableCell className="text-right">
                                <div className="flex justify-end gap-2">
                                  <Button
                                    variant="outline"
                                    size="icon"
                                    onClick={() => handlePreviewTemplate(template)}
                                    title="Preview"
                                  >
                                    <Eye className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="icon"
                                    onClick={() => {
                                      setSelectedTemplate(template);
                                      setIsEditDialogOpen(true);
                                    }}
                                    title="Edit"
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="icon"
                                    onClick={() => handleDuplicateTemplate(template)}
                                    title="Duplicate"
                                  >
                                    <Copy className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="icon"
                                    onClick={() => {
                                      setSelectedTemplate(template);
                                      setIsDeleteDialogOpen(true);
                                    }}
                                    title="Delete template"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create Template Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Create Email Template</DialogTitle>
            <DialogDescription>
              Create a new email template for your application
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Template Name</Label>
                <Input
                  id="name"
                  value={newTemplate.name}
                  onChange={(e) => setNewTemplate({ ...newTemplate, name: e.target.value })}
                  placeholder="Order Confirmation"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <Select
                  value={newTemplate.category}
                  onValueChange={(value) => setNewTemplate({ ...newTemplate, category: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={newTemplate.description}
                onChange={(e) => setNewTemplate({ ...newTemplate, description: e.target.value })}
                placeholder="Sent to customers after a successful purchase"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="subject">Subject</Label>
              <Input
                id="subject"
                value={newTemplate.subject}
                onChange={(e) => setNewTemplate({ ...newTemplate, subject: e.target.value })}
                placeholder="Your Order Confirmation - {{orderNumber}}"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="content">Email Content</Label>
              <EnhancedEmailEditor
                initialHtmlValue={newTemplate.htmlContent || ""}
                initialSubject={newTemplate.subject || ""}
                onHtmlChange={(value) => setNewTemplate({ ...newTemplate, htmlContent: value })}
                onSubjectChange={(value) => setNewTemplate({ ...newTemplate, subject: value })}
                height={300}
                showPreview={true}
                showSubject={false}
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="isDefault"
                checked={newTemplate.isDefault}
                onCheckedChange={(checked) => setNewTemplate({ ...newTemplate, isDefault: checked })}
              />
              <Label htmlFor="isDefault">Set as default template for this category</Label>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateTemplate}>Create Template</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Template Dialog */}
      {selectedTemplate && (
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle>Edit Email Template</DialogTitle>
              <DialogDescription>
                Update the email template details
              </DialogDescription>
            </DialogHeader>

            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-name">Template Name</Label>
                  <Input
                    id="edit-name"
                    value={selectedTemplate.name}
                    onChange={(e) => setSelectedTemplate({ ...selectedTemplate, name: e.target.value })}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="edit-category">Category</Label>
                  <Select
                    value={selectedTemplate.category}
                    onValueChange={(value) => setSelectedTemplate({ ...selectedTemplate, category: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-description">Description</Label>
                <Textarea
                  id="edit-description"
                  value={selectedTemplate.description}
                  onChange={(e) => setSelectedTemplate({ ...selectedTemplate, description: e.target.value })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-subject">Subject</Label>
                <Input
                  id="edit-subject"
                  value={selectedTemplate.subject}
                  onChange={(e) => setSelectedTemplate({ ...selectedTemplate, subject: e.target.value })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-content">Email Content</Label>
                <EnhancedEmailEditor
                  initialHtmlValue={selectedTemplate.htmlContent || ""}
                  initialSubject={selectedTemplate.subject || ""}
                  onHtmlChange={(value) => setSelectedTemplate({ ...selectedTemplate, htmlContent: value })}
                  onSubjectChange={(value) => setSelectedTemplate({ ...selectedTemplate, subject: value })}
                  height={300}
                  showPreview={true}
                  showSubject={false}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="edit-isDefault"
                  checked={selectedTemplate.isDefault}
                  onCheckedChange={(checked) => setSelectedTemplate({ ...selectedTemplate, isDefault: checked })}
                />
                <Label htmlFor="edit-isDefault">Set as default template for this category</Label>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleUpdateTemplate}>Save Changes</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Delete Template Confirmation */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the
              email template "{selectedTemplate?.name}".
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteTemplate} className="bg-destructive text-destructive-foreground">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Clear All Templates Confirmation */}
      <AlertDialog open={isClearAllDialogOpen} onOpenChange={setIsClearAllDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Clear All Email Templates?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete all non-default email templates
              ({templates.filter(t => !t.isDefault).length} templates). Default templates will be preserved.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleClearAllTemplates} className="bg-destructive text-destructive-foreground">
              Clear All Templates
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Force Clear ALL Templates Confirmation */}
      <AlertDialog open={isForceClearAllDialogOpen} onOpenChange={setIsForceClearAllDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>⚠️ FORCE CLEAR ALL EMAIL TEMPLATES?</AlertDialogTitle>
            <AlertDialogDescription>
              <strong>WARNING:</strong> This action cannot be undone! This will permanently delete ALL email templates
              ({templates.length} templates), including default templates. You will need to create new templates from scratch.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleForceClearAllTemplates} className="bg-red-600 text-white hover:bg-red-700">
              ⚠️ FORCE DELETE ALL
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Preview Template Dialog */}
      <Dialog open={isPreviewDialogOpen} onOpenChange={setIsPreviewDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle>Email Preview</DialogTitle>
            <DialogDescription>
              Preview how the email will look to recipients
            </DialogDescription>
          </DialogHeader>

          {previewData && (
            <ScrollArea className="h-[60vh]">
              <div className="space-y-4">
                <div className="p-4 border rounded-md">
                  <h3 className="font-medium mb-2">Subject:</h3>
                  <p>{previewData.subject}</p>
                </div>

                <div className="p-4 border rounded-md">
                  <h3 className="font-medium mb-2">Email Body:</h3>
                  <div
                    className="prose prose-sm max-w-none"
                    dangerouslySetInnerHTML={{ __html: previewData.htmlContent }}
                  />
                </div>
              </div>
            </ScrollArea>
          )}

          <DialogFooter>
            <Button onClick={() => setIsPreviewDialogOpen(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
}
