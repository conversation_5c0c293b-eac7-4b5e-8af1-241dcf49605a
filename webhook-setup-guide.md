# 🔗 Telegram Bot Webhook Setup Guide

## 🎯 **Overview**

Your Telegram bot now supports both **Polling** and **Webhook** modes. Here's how to set up webhooks for your live server deployment.

## 📋 **Prerequisites**

- ✅ Live server with a domain (e.g., `yourdomain.com`)
- ✅ SSL certificate (HTTPS required)
- ✅ Telegram bot token
- ✅ Admin Chat ID

## 🚀 **Quick Setup for Live Server**

### 1. **Update Webhook URL**
```
https://yourdomain.com/api/telegram/webhook
```

### 2. **Set Webhook via Admin Dashboard**
1. Go to **Admin → Telegram Bot Settings**
2. Enter your webhook URL in the "Webhook URL" field
3. Click **"Test"** to validate the URL
4. Click **"Set"** to configure the webhook
5. Verify success message

### 3. **Alternative: Set Webhook via API**
```bash
curl -X POST "https://yourdomain.com/api/telegram/set-webhook" \
  -H "Content-Type: application/json" \
  -d '{"webhookUrl": "https://yourdomain.com/api/telegram/webhook"}'
```

## 🧪 **Testing Webhook**

### **Test URL Accessibility**
```bash
curl -X POST "https://yourdomain.com/api/telegram/webhook" \
  -H "Content-Type: application/json" \
  -d '{"test": true}'
```

**Expected Response:**
```json
{
  "ok": true,
  "message": "Webhook endpoint is accessible",
  "timestamp": "2025-01-06T18:00:00.000Z"
}
```

### **Verify Webhook Status**
```bash
curl "https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getWebhookInfo"
```

## 🔧 **Webhook Features**

### ✅ **Enhanced Webhook Endpoint**
- **URL Validation**: Ensures HTTPS protocol
- **Test Requests**: Handles validation tests
- **Async Processing**: Prevents timeout issues
- **Error Handling**: Robust error management

### ✅ **Admin Dashboard Features**
- **Test Button**: Validate webhook accessibility
- **Set Button**: Configure webhook with Telegram
- **Remove Button**: Remove webhook and return to polling
- **Status Display**: Shows current webhook state

### ✅ **API Endpoints**
- `POST /api/telegram/set-webhook` - Set webhook URL
- `POST /api/telegram/validate-webhook` - Test webhook accessibility
- `POST /api/telegram/remove-webhook` - Remove webhook
- `GET /api/telegram/webhook-info` - Get webhook status
- `POST /api/telegram/webhook` - Webhook endpoint

## 🔄 **Switching Between Modes**

### **From Polling to Webhook:**
1. Set webhook URL in admin dashboard
2. Click "Set Webhook"
3. Polling automatically stops

### **From Webhook to Polling:**
1. Click "Remove Webhook" in admin dashboard
2. Polling automatically resumes

## 🛡️ **Security Features**

- **HTTPS Validation**: Only HTTPS URLs accepted
- **URL Format Validation**: Proper URL structure required
- **Timeout Protection**: 10-second response guarantee
- **Error Recovery**: Graceful fallback handling

## 📊 **Performance Benefits**

| Feature | Polling | Webhook |
|---------|---------|---------|
| Response Time | ~1 second | Instant |
| Server Load | Higher | Lower |
| Bandwidth | More | Less |
| Scalability | Limited | Excellent |

## 🐛 **Troubleshooting**

### **Common Issues:**

1. **"Webhook URL must use HTTPS"**
   - Ensure your domain has SSL certificate
   - Use `https://` not `http://`

2. **"Webhook URL is not accessible"**
   - Check firewall settings
   - Verify domain DNS resolution
   - Test URL manually

3. **"Bot not responding to buttons"**
   - Webhook might not be set correctly
   - Check webhook status via API
   - Try removing and re-setting webhook

### **Debug Commands:**
```bash
# Check webhook status
curl "https://api.telegram.org/bot<TOKEN>/getWebhookInfo"

# Test webhook endpoint
curl -X POST "https://yourdomain.com/api/telegram/webhook" \
  -H "Content-Type: application/json" \
  -d '{"test": true}'

# Remove webhook (fallback to polling)
curl -X POST "https://yourdomain.com/api/telegram/remove-webhook"
```

## 🎉 **Success Indicators**

✅ **Webhook Working:**
- Instant bot responses
- Interactive buttons work
- No polling logs in server
- Webhook status shows active URL

✅ **All Features Working:**
- `/orders` command shows orders
- "Mark as Paid" buttons work
- "Choose Template" buttons work
- Email sending functions

## 📞 **Support**

If you encounter issues:
1. Check server logs for errors
2. Verify webhook URL accessibility
3. Test with polling mode first
4. Use admin dashboard test buttons

Your webhook implementation is now ready for production! 🚀
