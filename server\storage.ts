import {
  users,
  products,
  invoices,
  customCheckoutPages,
  allowedEmails,
  emailTemplates,
  paypalButtons,
  customInvoices,
  systemMessages,
  type User,
  type InsertUser,
  type Product,
  type InsertProduct,
  type Invoice,
  type InsertInvoice,
  type CustomCheckoutPage,
  type InsertCustomCheckoutPage,
  type AllowedEmail,
  type InsertAllowedEmail,
  type EmailTemplate,
  type InsertEmailTemplate,
  type PaypalButton,
  type InsertPaypalButton,
  type CustomInvoice,
  type InsertCustomInvoice,
  smtpProviders,
  type SmtpProvider,
  type InsertSmtpProvider
} from "@shared/schema";
import { EmbedCode } from '@shared/embed-codes';
import { createHash, randomBytes } from 'crypto';
import { db } from './db';
import { eq, sql, and } from 'drizzle-orm';

// Device interface
interface Device {
  id: string;
  name: string;
  ip: string;
  userAgent: string;
  lastLogin: string;
  createdAt: string;
}

// Recovery code interface
interface RecoveryCode {
  code: string;
  used: boolean;
}

export interface IStorage {
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  verifyUserCredentials(username: string, password: string): Promise<boolean>;
  verifyUserCredentialsWithUser(user: User, password: string): Promise<boolean>;
  saveResetToken(userId: number, token: string, expiry: Date): Promise<void>;
  validateResetToken(token: string): Promise<boolean>;
  getUserByResetToken(token: string): Promise<User | undefined>;
  updateUserPassword(userId: number, password: string): Promise<void>;
  clearResetToken(userId: number): Promise<void>;
  updateUsername(userId: number, username: string): Promise<void>;
  updateAutoLoginSettings(userId: number, rememberMe: boolean): Promise<void>;
  enableTwoFactor(userId: number, secret: string): Promise<void>;
  disableTwoFactor(userId: number): Promise<void>;
  verifyTwoFactorToken(userId: number, token: string): Promise<boolean>;

  // Recovery code methods
  generateRecoveryCodes(userId: number): Promise<string[]>;
  verifyRecoveryCode(userId: number, code: string): Promise<boolean>;

  // Device tracking methods
  addDevice(userId: number, deviceInfo: Omit<Device, 'id' | 'createdAt' | 'lastLogin'>): Promise<Device>;
  getDevices(userId: number): Promise<Device[]>;
  updateDeviceLastLogin(userId: number, deviceId: string): Promise<void>;
  removeDevice(userId: number, deviceId: string): Promise<boolean>;

  // Product methods
  getProducts(): Promise<Product[]>;
  getProduct(id: number): Promise<Product | undefined>;
  createProduct(product: InsertProduct): Promise<Product>;
  deleteProduct(id: number): Promise<boolean>;
  bulkDeleteProducts(ids: number[]): Promise<{ success: number; failed: number }>;

  // Invoice methods
  createInvoice(invoice: InsertInvoice): Promise<Invoice>;
  getInvoice(id: number): Promise<Invoice | undefined>;
  getInvoices(): Promise<Invoice[]>;
  updateInvoice(id: number, update: Partial<InsertInvoice>): Promise<Invoice | undefined>;
  deleteInvoice(id: number): Promise<boolean>;
  bulkDeleteInvoices(ids: number[]): Promise<{ success: number; failed: number }>;

  // Configuration methods
  getGeneralSettings(): Promise<any>;
  getEmailConfig(): Promise<any>;
  getPaymentConfig(): Promise<any>;

  // Custom Checkout Page methods
  createCustomCheckoutPage(page: InsertCustomCheckoutPage): Promise<CustomCheckoutPage>;
  getCustomCheckoutPage(id: number): Promise<CustomCheckoutPage | undefined>;
  getCustomCheckoutPageBySlug(slug: string): Promise<CustomCheckoutPage | undefined>;
  getCustomCheckoutPages(): Promise<CustomCheckoutPage[]>;
  updateCustomCheckoutPage(id: number, update: Partial<InsertCustomCheckoutPage>): Promise<CustomCheckoutPage | undefined>;
  incrementCustomCheckoutPageViews(id: number): Promise<void>;
  incrementCustomCheckoutPageConversions(id: number): Promise<void>;
  deleteCustomCheckoutPage(id: number): Promise<boolean>;

  // Allowed Email methods
  getAllowedEmails(): Promise<AllowedEmail[]>;
  getAllowedEmail(id: number): Promise<AllowedEmail | undefined>;
  getEmailByAddress(email: string): Promise<AllowedEmail | undefined>;
  isEmailAllowed(email: string): Promise<boolean>;
  createAllowedEmail(email: InsertAllowedEmail): Promise<AllowedEmail>;
  updateAllowedEmail(id: number, update: Partial<InsertAllowedEmail>): Promise<AllowedEmail | undefined>;
  updateOrCreateAllowedEmail(emailAddress: string, update: Partial<InsertAllowedEmail>): Promise<AllowedEmail>;
  deleteAllowedEmail(id: number): Promise<boolean>;
  bulkCreateAllowedEmails(emails: string[]): Promise<{ success: number; failed: number }>;
  bulkDeleteAllowedEmails(ids: number[]): Promise<{ success: number; failed: number }>;

  // Email Template methods
  getEmailTemplates(): Promise<EmailTemplate[]>;
  getEmailTemplate(id: number): Promise<EmailTemplate | undefined>;
  createEmailTemplate(template: InsertEmailTemplate): Promise<EmailTemplate>;
  updateEmailTemplate(id: number, update: Partial<InsertEmailTemplate>): Promise<EmailTemplate | undefined>;
  deleteEmailTemplate(id: number): Promise<boolean>;

  // PayPal Button methods
  getPaypalButtons(): Promise<PaypalButton[]>;
  getPaypalButton(id: number): Promise<PaypalButton | undefined>;
  createPaypalButton(button: InsertPaypalButton): Promise<PaypalButton>;
  updatePaypalButton(id: number, update: Partial<InsertPaypalButton>): Promise<PaypalButton | undefined>;
  deletePaypalButton(id: number): Promise<boolean>;

  // Custom Invoice methods
  getCustomInvoices(): Promise<CustomInvoice[]>;
  getCustomInvoice(id: number): Promise<CustomInvoice | undefined>;
  getCustomInvoiceByNumber(invoiceNumber: string): Promise<CustomInvoice | undefined>;
  createCustomInvoice(invoice: InsertCustomInvoice): Promise<CustomInvoice>;
  updateCustomInvoice(id: number, update: Partial<InsertCustomInvoice>): Promise<CustomInvoice | undefined>;
  incrementCustomInvoiceViewCount(id: number): Promise<void>;
  markCustomInvoiceAsPaid(id: number): Promise<CustomInvoice | undefined>;
  deleteCustomInvoice(id: number): Promise<boolean>;

  // Contact Inquiry methods
  createContactInquiry?(inquiry: any): Promise<any>;
  getContactInquiries?(): Promise<any[]>;
  updateContactInquiry?(id: number, update: any): Promise<any>;

  // Embed Code methods
  getEmbedCodes(): Promise<EmbedCode[]>;
  getEmbedCode(id: string): Promise<EmbedCode | undefined>;
  createEmbedCode(embedCode: EmbedCode): Promise<EmbedCode>;
  updateEmbedCode(id: string, update: Partial<EmbedCode>): Promise<EmbedCode | undefined>;
  deleteEmbedCode(id: string): Promise<boolean>;
}

// Legacy MemStorage class removed - using DatabaseStorage with MySQL instead























// Database Storage Implementation
export class DatabaseStorage implements IStorage {
  private db: typeof db;

  constructor() {
    this.db = db;
  }

  // User methods
  async getUser(id: number): Promise<User | undefined> {
    try {
      const result = await this.db.select().from(users).where(eq(users.id, id));
      return result[0];
    } catch (error) {
      console.error('Error getting user:', error);
      return undefined;
    }
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    try {
      const result = await this.db.select().from(users).where(eq(users.username, username));
      return result[0];
    } catch (error) {
      console.error('Error getting user by username:', error);
      return undefined;
    }
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    // Note: The current schema doesn't have email field, so we'll return undefined
    // This can be extended when email field is added to users table
    return undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    try {
      // Hash the password if it's not already hashed
      let password = insertUser.password;
      if (!password.match(/^[0-9a-f]{64}$/i)) {
        password = createHash('sha256').update(password).digest('hex');
      }

      const userToInsert = {
        username: insertUser.username,
        password: password
      };

      const result = await this.db.insert(users).values(userToInsert);
      const insertId = result.insertId;

      // Fetch the created user
      const createdUser = await this.db.select().from(users).where(eq(users.id, insertId)).limit(1);
      return createdUser[0] as User;
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  async verifyUserCredentials(username: string, password: string): Promise<boolean> {
    const user = await this.getUserByUsername(username);
    if (!user) return false;

    // Hash the password for comparison
    const hashedPassword = createHash('sha256').update(password).digest('hex');
    return user.password === hashedPassword;
  }

  async verifyUserCredentialsWithUser(user: User, password: string): Promise<boolean> {
    if (!user) return false;

    // Hash the password for comparison
    const hashedPassword = createHash('sha256').update(password).digest('hex');
    return user.password === hashedPassword;
  }

  // For now, these methods will be no-ops since the current schema doesn't support them
  // They can be implemented when the schema is extended
  async saveResetToken(userId: number, token: string, expiry: Date): Promise<void> {
    // TODO: Implement when schema supports reset tokens
  }

  async validateResetToken(token: string): Promise<boolean> {
    // TODO: Implement when schema supports reset tokens
    return false;
  }

  async getUserByResetToken(token: string): Promise<User | undefined> {
    // TODO: Implement when schema supports reset tokens
    return undefined;
  }

  async updateUserPassword(userId: number, password: string): Promise<void> {
    try {
      const hashedPassword = createHash('sha256').update(password).digest('hex');
      await this.db.update(users).set({ password: hashedPassword }).where(eq(users.id, userId));
    } catch (error) {
      console.error('Error updating user password:', error);
    }
  }

  async clearResetToken(userId: number): Promise<void> {
    // TODO: Implement when schema supports reset tokens
  }

  async updateUsername(userId: number, username: string): Promise<void> {
    try {
      await this.db.update(users).set({ username }).where(eq(users.id, userId));
    } catch (error) {
      console.error('Error updating username:', error);
    }
  }

  async updateAutoLoginSettings(userId: number, rememberMe: boolean): Promise<void> {
    // TODO: Implement when schema supports rememberMe field
  }

  async enableTwoFactor(userId: number, secret: string): Promise<void> {
    // TODO: Implement when schema supports 2FA fields
  }

  async disableTwoFactor(userId: number): Promise<void> {
    // TODO: Implement when schema supports 2FA fields
  }

  async verifyTwoFactorToken(userId: number, token: string): Promise<boolean> {
    // TODO: Implement when schema supports 2FA fields
    return false;
  }

  async generateRecoveryCodes(userId: number): Promise<string[]> {
    // TODO: Implement when schema supports recovery codes
    return [];
  }

  async verifyRecoveryCode(userId: number, code: string): Promise<boolean> {
    // TODO: Implement when schema supports recovery codes
    return false;
  }

  async addDevice(userId: number, deviceInfo: Omit<Device, 'id' | 'createdAt' | 'lastLogin'>): Promise<Device> {
    // TODO: Implement when schema supports device tracking
    const device: Device = {
      ...deviceInfo,
      id: randomBytes(16).toString('hex'),
      createdAt: new Date().toISOString(),
      lastLogin: new Date().toISOString()
    };
    return device;
  }

  async getDevices(userId: number): Promise<Device[]> {
    // TODO: Implement when schema supports device tracking
    return [];
  }

  async updateDeviceLastLogin(userId: number, deviceId: string): Promise<void> {
    // TODO: Implement when schema supports device tracking
  }

  async removeDevice(userId: number, deviceId: string): Promise<boolean> {
    // TODO: Implement when schema supports device tracking
    return false;
  }

  // Product methods
  async getProducts(): Promise<Product[]> {
    try {
      const result = await this.db.select().from(products);
      return result;
    } catch (error) {
      console.error('Error getting products:', error);
      return [];
    }
  }

  async getProduct(id: number): Promise<Product | undefined> {
    try {
      const result = await this.db.select().from(products).where(eq(products.id, id));
      return result[0];
    } catch (error) {
      console.error('Error getting product:', error);
      return undefined;
    }
  }

  async createProduct(insertProduct: InsertProduct): Promise<Product> {
    try {
      // Insert the product
      await this.db.insert(products).values(insertProduct);

      // Get the last inserted product by name (since MySQL doesn't support returning)
      const result = await this.db.select().from(products)
        .where(eq(products.name, insertProduct.name))
        .orderBy(sql`id DESC`)
        .limit(1);

      if (result.length === 0) {
        throw new Error('Failed to retrieve created product');
      }

      return result[0] as Product;
    } catch (error) {
      console.error('Error creating product:', error);
      throw error;
    }
  }

  async deleteProduct(id: number): Promise<boolean> {
    try {
      const result = await this.db.delete(products).where(eq(products.id, id));
      return true; // MySQL doesn't return affected rows count in this setup
    } catch (error) {
      console.error('Error deleting product:', error);
      return false;
    }
  }

  async bulkDeleteProducts(ids: number[]): Promise<{ success: number; failed: number }> {
    let success = 0;
    let failed = 0;

    for (const id of ids) {
      try {
        await this.db.delete(products).where(eq(products.id, id));
        success++;
      } catch (error) {
        console.error(`Error deleting product ${id}:`, error);
        failed++;
      }
    }

    return { success, failed };
  }

  // Invoice methods
  async createInvoice(insertInvoice: InsertInvoice): Promise<Invoice> {
    try {
      console.log('Creating invoice with data:', insertInvoice);

      // Insert the invoice
      await this.db.insert(invoices).values(insertInvoice);

      // Get the last inserted invoice by using the paypalInvoiceId as unique identifier
      // Since MySQL doesn't support RETURNING, we'll use the unique paypalInvoiceId
      if (insertInvoice.paypalInvoiceId) {
        const result = await this.db.select().from(invoices)
          .where(eq(invoices.paypalInvoiceId, insertInvoice.paypalInvoiceId))
          .orderBy(sql`id DESC`)
          .limit(1);

        if (result.length > 0) {
          return result[0] as Invoice;
        }
      }

      // Fallback: get the latest invoice by email and amount
      if (insertInvoice.customerEmail && insertInvoice.amount) {
        const result = await this.db.select().from(invoices)
          .where(
            and(
              eq(invoices.customerEmail, insertInvoice.customerEmail),
              eq(invoices.amount, insertInvoice.amount)
            )
          )
          .orderBy(sql`id DESC`)
          .limit(1);

        if (result.length > 0) {
          return result[0] as Invoice;
        }
      }

      // Final fallback: get the very latest invoice
      const result = await this.db.select().from(invoices)
        .orderBy(sql`id DESC`)
        .limit(1);

      if (result.length === 0) {
        throw new Error('Failed to retrieve created invoice');
      }

      return result[0] as Invoice;
    } catch (error) {
      console.error('Error creating invoice:', error);
      throw error;
    }
  }

  async getInvoice(id: number): Promise<Invoice | undefined> {
    try {
      const result = await this.db.select().from(invoices).where(eq(invoices.id, id));
      return result[0];
    } catch (error) {
      console.error('Error getting invoice:', error);
      return undefined;
    }
  }

  async getInvoices(): Promise<Invoice[]> {
    try {
      const result = await this.db.select().from(invoices);
      return result;
    } catch (error) {
      console.error('Error getting invoices:', error);
      return [];
    }
  }

  async updateInvoice(id: number, update: Partial<InsertInvoice>): Promise<Invoice | undefined> {
    try {
      // Update the invoice
      await this.db.update(invoices).set(update).where(eq(invoices.id, id));

      // Get the updated invoice
      const result = await this.db.select().from(invoices).where(eq(invoices.id, id));
      return result[0] as Invoice;
    } catch (error) {
      console.error('Error updating invoice:', error);
      return undefined;
    }
  }

  async deleteInvoice(id: number): Promise<boolean> {
    try {
      const result = await this.db.delete(invoices).where(eq(invoices.id, id));
      return true; // MySQL doesn't return affected rows count in this setup
    } catch (error) {
      console.error('Error deleting invoice:', error);
      return false;
    }
  }

  async bulkDeleteInvoices(ids: number[]): Promise<{ success: number; failed: number }> {
    let success = 0;
    let failed = 0;

    for (const id of ids) {
      try {
        await this.db.delete(invoices).where(eq(invoices.id, id));
        success++;
      } catch (error) {
        console.error(`Error deleting invoice ${id}:`, error);
        failed++;
      }
    }

    return { success, failed };
  }

  // Configuration methods
  async getGeneralSettings(): Promise<any> {
    const { databaseConfig } = await import('./services/database-config');
    return databaseConfig.getConfig('general', {
      siteName: "TemplateHub Pro",
      siteDescription: "Premium productivity app templates and UI/UX design systems",
      logoUrl: "",
      faviconUrl: "",
      primaryColor: "#6366f1",
      secondaryColor: "#4f46e5",
      footerText: "© 2024 TemplateHub Pro",
      enableCheckout: true,
      enableCustomCheckout: true,
      enableTestMode: true,
      defaultTestCustomer: {
        enabled: true,
        name: "Test Designer",
        email: "<EMAIL>"
      },
      emailDomainRestriction: {
        enabled: false,
        allowedDomains: "gmail.com, hotmail.com, yahoo.com"
      }
    });
  }

  async getEmailConfig(): Promise<any> {
    try {
      // Get SMTP providers from database
      const providers = await this.db.select().from(smtpProviders);

      // Transform database records to the expected format
      const transformedProviders = providers.map(provider => ({
        id: provider.id,
        name: provider.name,
        active: Boolean(provider.active),
        isDefault: Boolean(provider.isDefault),
        isBackup: Boolean(provider.isBackup),
        credentials: {
          host: provider.host,
          port: provider.port,
          secure: Boolean(provider.secure),
          auth: {
            user: provider.username,
            pass: provider.password
          },
          fromEmail: provider.fromEmail,
          fromName: provider.fromName
        }
      }));

      return {
        providers: transformedProviders
      };
    } catch (error) {
      console.error('Error fetching SMTP providers from database:', error);

      // Fallback to hardcoded config if database fails
      return {
        providers: [
          {
            id: 'smtp-1',
            name: 'Primary SMTP',
            active: true,
            isDefault: true,
            isBackup: false,
            credentials: {
              host: 'smtp-relay.brevo.com',
              port: '587',
              secure: false,
              auth: {
                user: '<EMAIL>',
                pass: '3d8I9xFm1yMDYj7W'
              },
              fromEmail: '<EMAIL>',
              fromName: 'PayPal Invoicer'
            }
          }
        ]
      };
    }
  }

  async createSmtpProvider(provider: any): Promise<any> {
    try {
      const result = await this.db.insert(smtpProviders).values({
        id: provider.id,
        name: provider.name,
        host: provider.host,
        port: provider.port,
        secure: provider.secure,
        username: provider.username,
        password: provider.password,
        fromEmail: provider.fromEmail,
        fromName: provider.fromName,
        active: provider.active,
        isDefault: provider.isDefault,
        isBackup: provider.isBackup,
        createdAt: provider.createdAt,
        updatedAt: provider.updatedAt
      });

      console.log(`✅ SMTP provider created in database: ${provider.id}`);
      return result;
    } catch (error) {
      console.error('Error creating SMTP provider:', error);
      throw error;
    }
  }



  async saveSmtpProvider(provider: any): Promise<void> {
    try {
      const now = new Date().toISOString();

      // If this is set as default, unset all other defaults first
      if (provider.isDefault) {
        await this.db.update(smtpProviders)
          .set({ isDefault: false, updatedAt: now })
          .where(sql`is_default = 1`);
      }

      // If this is set as backup, unset all other backups first
      if (provider.isBackup) {
        await this.db.update(smtpProviders)
          .set({ isBackup: false, updatedAt: now })
          .where(sql`is_backup = 1`);
      }

      // Check if provider exists
      const existingProvider = await this.db.select().from(smtpProviders).where(eq(smtpProviders.id, provider.id));

      if (existingProvider.length > 0) {
        // Update existing provider
        await this.db.update(smtpProviders)
          .set({
            name: provider.name,
            host: provider.host,
            port: provider.port,
            secure: provider.secure ? true : false,
            username: provider.username,
            password: provider.password,
            fromEmail: provider.fromEmail,
            fromName: provider.fromName,
            active: provider.active ? true : false,
            isDefault: provider.isDefault ? true : false,
            isBackup: provider.isBackup ? true : false,
            updatedAt: now
          })
          .where(eq(smtpProviders.id, provider.id));
      } else {
        // Insert new provider
        await this.db.insert(smtpProviders).values({
          id: provider.id,
          name: provider.name,
          host: provider.host,
          port: provider.port,
          secure: provider.secure ? true : false,
          username: provider.username,
          password: provider.password,
          fromEmail: provider.fromEmail,
          fromName: provider.fromName,
          active: provider.active ? true : false,
          isDefault: provider.isDefault ? true : false,
          isBackup: provider.isBackup ? true : false,
          createdAt: now,
          updatedAt: now
        });
      }

      console.log(`SMTP provider ${provider.id} saved successfully`);
    } catch (error) {
      console.error('Error saving SMTP provider:', error);
      throw error;
    }
  }

  async exportSmtpProviders(): Promise<any> {
    try {
      const providers = await this.db.select().from(smtpProviders);

      const exportData = {
        exportDate: new Date().toISOString(),
        version: '1.0',
        smtpProviders: providers.map(provider => ({
          id: provider.id,
          name: provider.name,
          host: provider.host,
          port: provider.port,
          secure: Boolean(provider.secure),
          username: provider.username,
          password: provider.password,
          fromEmail: provider.fromEmail,
          fromName: provider.fromName,
          active: Boolean(provider.active),
          isDefault: Boolean(provider.isDefault),
          isBackup: Boolean(provider.isBackup),
          createdAt: provider.createdAt,
          updatedAt: provider.updatedAt
        }))
      };

      console.log(`Exported ${providers.length} SMTP providers`);
      return exportData;
    } catch (error) {
      console.error('Error exporting SMTP providers:', error);
      throw error;
    }
  }

  async importSmtpProviders(importData: any, options: { replaceExisting?: boolean, preserveIds?: boolean } = {}): Promise<{ imported: number, skipped: number, errors: string[] }> {
    try {
      const { replaceExisting = false, preserveIds = true } = options;
      const results = { imported: 0, skipped: 0, errors: [] as string[] };

      if (!importData.smtpProviders || !Array.isArray(importData.smtpProviders)) {
        throw new Error('Invalid import data: smtpProviders array not found');
      }

      // Get existing providers
      const existingProviders = await this.db.select().from(smtpProviders);
      const existingIds = new Set(existingProviders.map(p => p.id));

      for (const providerData of importData.smtpProviders) {
        try {
          const providerId = preserveIds ? providerData.id : `smtp-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

          // Check if provider already exists
          if (existingIds.has(providerId)) {
            if (!replaceExisting) {
              results.skipped++;
              continue;
            }

            // Update existing provider
            const now = new Date().toISOString();
            await this.db.update(smtpProviders)
              .set({
                name: providerData.name,
                host: providerData.host,
                port: providerData.port,
                secure: providerData.secure ? 1 : 0,
                username: providerData.username,
                password: providerData.password,
                fromEmail: providerData.fromEmail,
                fromName: providerData.fromName,
                active: providerData.active ? 1 : 0,
                isDefault: providerData.isDefault ? 1 : 0,
                isBackup: providerData.isBackup ? 1 : 0,
                updatedAt: now
              })
              .where(eq(smtpProviders.id, providerId));
          } else {
            // Insert new provider
            const now = new Date().toISOString();
            await this.db.insert(smtpProviders).values({
              id: providerId,
              name: providerData.name,
              host: providerData.host,
              port: providerData.port,
              secure: providerData.secure ? 1 : 0,
              username: providerData.username,
              password: providerData.password,
              fromEmail: providerData.fromEmail,
              fromName: providerData.fromName,
              active: providerData.active ? 1 : 0,
              isDefault: providerData.isDefault ? 1 : 0,
              isBackup: providerData.isBackup ? 1 : 0,
              createdAt: providerData.createdAt || now,
              updatedAt: now
            });
          }

          results.imported++;
        } catch (error) {
          results.errors.push(`Failed to import provider ${providerData.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      console.log(`SMTP import completed: ${results.imported} imported, ${results.skipped} skipped, ${results.errors.length} errors`);
      return results;
    } catch (error) {
      console.error('Error importing SMTP providers:', error);
      throw error;
    }
  }

  async getPaymentConfig(): Promise<any> {
    const { databaseConfig } = await import('./services/database-config');
    return databaseConfig.getPaymentConfig();
  }

  // Custom Checkout Pages - MySQL Implementation
  async createCustomCheckoutPage(page: InsertCustomCheckoutPage): Promise<CustomCheckoutPage> {
    try {
      const result = await this.db.insert(customCheckoutPages).values(page);
      const insertId = result.insertId;

      // Fetch the created page
      const createdPage = await this.db.select().from(customCheckoutPages).where(eq(customCheckoutPages.id, insertId)).limit(1);
      return createdPage[0];
    } catch (error) {
      console.error('Error creating custom checkout page:', error);
      throw error;
    }
  }

  async getCustomCheckoutPage(id: number): Promise<CustomCheckoutPage | undefined> {
    try {
      const result = await this.db.select().from(customCheckoutPages).where(eq(customCheckoutPages.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting custom checkout page:', error);
      return undefined;
    }
  }

  async getCustomCheckoutPageBySlug(slug: string): Promise<CustomCheckoutPage | undefined> {
    try {
      const result = await this.db.select().from(customCheckoutPages).where(eq(customCheckoutPages.slug, slug)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting custom checkout page by slug:', error);
      return undefined;
    }
  }

  async getCustomCheckoutPages(): Promise<CustomCheckoutPage[]> {
    try {
      const result = await this.db.select().from(customCheckoutPages).orderBy(customCheckoutPages.createdAt);
      return result;
    } catch (error) {
      console.error('Error getting custom checkout pages:', error);
      return [];
    }
  }

  async updateCustomCheckoutPage(id: number, update: Partial<InsertCustomCheckoutPage>): Promise<CustomCheckoutPage | undefined> {
    try {
      await this.db.update(customCheckoutPages).set(update).where(eq(customCheckoutPages.id, id));

      // Fetch the updated page
      const updatedPage = await this.db.select().from(customCheckoutPages).where(eq(customCheckoutPages.id, id)).limit(1);
      return updatedPage[0];
    } catch (error) {
      console.error('Error updating custom checkout page:', error);
      return undefined;
    }
  }

  async incrementCustomCheckoutPageViews(id: number): Promise<void> {
    try {
      await this.db.update(customCheckoutPages)
        .set({ views: sql`${customCheckoutPages.views} + 1` })
        .where(eq(customCheckoutPages.id, id));
    } catch (error) {
      console.error('Error incrementing custom checkout page views:', error);
    }
  }

  async incrementCustomCheckoutPageConversions(id: number): Promise<void> {
    try {
      await this.db.update(customCheckoutPages)
        .set({ conversions: sql`${customCheckoutPages.conversions} + 1` })
        .where(eq(customCheckoutPages.id, id));
    } catch (error) {
      console.error('Error incrementing custom checkout page conversions:', error);
    }
  }

  async deleteCustomCheckoutPage(id: number): Promise<boolean> {
    try {
      const result = await this.db.delete(customCheckoutPages).where(eq(customCheckoutPages.id, id));
      return result.rowsAffected > 0;
    } catch (error) {
      console.error('Error deleting custom checkout page:', error);
      return false;
    }
  }

  // Allowed Emails - MySQL Implementation
  async getAllowedEmails(): Promise<AllowedEmail[]> {
    try {
      const result = await this.db.select().from(allowedEmails).orderBy(allowedEmails.createdAt);
      return result;
    } catch (error) {
      console.error('Error getting allowed emails:', error);
      return [];
    }
  }

  async getAllowedEmail(id: number): Promise<AllowedEmail | undefined> {
    try {
      const result = await this.db.select().from(allowedEmails).where(eq(allowedEmails.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting allowed email:', error);
      return undefined;
    }
  }

  async getEmailByAddress(email: string): Promise<AllowedEmail | undefined> {
    try {
      const result = await this.db.select().from(allowedEmails).where(eq(allowedEmails.email, email)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting email by address:', error);
      return undefined;
    }
  }

  async isEmailAllowed(email: string): Promise<boolean> {
    try {
      const result = await this.db.select().from(allowedEmails).where(eq(allowedEmails.email, email)).limit(1);
      return result.length > 0;
    } catch (error) {
      console.error('Error checking if email is allowed:', error);
      return false;
    }
  }

  async createAllowedEmail(email: InsertAllowedEmail): Promise<AllowedEmail> {
    try {
      const result = await this.db.insert(allowedEmails).values(email);
      const insertId = result.insertId;

      // Fetch the created email
      const createdEmail = await this.db.select().from(allowedEmails).where(eq(allowedEmails.id, insertId)).limit(1);
      return createdEmail[0];
    } catch (error) {
      console.error('Error creating allowed email:', error);
      throw error;
    }
  }

  async updateAllowedEmail(id: number, update: Partial<InsertAllowedEmail>): Promise<AllowedEmail | undefined> {
    try {
      await this.db.update(allowedEmails).set(update).where(eq(allowedEmails.id, id));

      // Fetch the updated email
      const updatedEmail = await this.db.select().from(allowedEmails).where(eq(allowedEmails.id, id)).limit(1);
      return updatedEmail[0];
    } catch (error) {
      console.error('Error updating allowed email:', error);
      return undefined;
    }
  }

  async updateOrCreateAllowedEmail(emailAddress: string, update: Partial<InsertAllowedEmail>): Promise<AllowedEmail> {
    try {
      const existing = await this.getEmailByAddress(emailAddress);
      if (existing) {
        const updated = await this.updateAllowedEmail(existing.id, update);
        return updated || existing;
      } else {
        return await this.createAllowedEmail({ ...update, email: emailAddress } as InsertAllowedEmail);
      }
    } catch (error) {
      console.error('Error updating or creating allowed email:', error);
      throw error;
    }
  }

  async deleteAllowedEmail(id: number): Promise<boolean> {
    try {
      console.log(`🗑️ Attempting to delete allowed email with ID: ${id}`);

      // First check if the email exists
      const existingEmail = await this.db.select().from(allowedEmails).where(eq(allowedEmails.id, id)).limit(1);
      console.log(`🔍 Email exists check:`, existingEmail);

      if (existingEmail.length === 0) {
        console.log(`❌ Email with ID ${id} not found`);
        return false;
      }

      const result = await this.db.delete(allowedEmails).where(eq(allowedEmails.id, id));
      console.log(`🗑️ Delete result:`, result);

      // Check different possible property names for affected rows
      const affectedRows = result.rowsAffected || result.affectedRows || result.changes || 0;
      console.log(`📊 Affected rows: ${affectedRows}`);

      return affectedRows > 0;
    } catch (error) {
      console.error('❌ Error deleting allowed email:', error);
      return false;
    }
  }

  async bulkCreateAllowedEmails(emails: string[]): Promise<{ success: number; failed: number }> {
    let success = 0;
    let failed = 0;

    for (const email of emails) {
      try {
        await this.createAllowedEmail({
          email,
          notes: 'Bulk imported',
          smtpProvider: 'avixiptv-smtp',
          lastUpdated: new Date().toISOString(),
          createdAt: new Date().toISOString()
        });
        success++;
      } catch (error) {
        failed++;
      }
    }

    return { success, failed };
  }

  async bulkDeleteAllowedEmails(ids: number[]): Promise<{ success: number; failed: number }> {
    let success = 0;
    let failed = 0;

    for (const id of ids) {
      try {
        const deleted = await this.deleteAllowedEmail(id);
        if (deleted) success++;
        else failed++;
      } catch (error) {
        failed++;
      }
    }

    return { success, failed };
  }

  // Email Templates - MySQL Implementation
  async getEmailTemplates(): Promise<EmailTemplate[]> {
    try {
      const result = await this.db.select().from(emailTemplates).orderBy(emailTemplates.createdAt);
      return result;
    } catch (error) {
      console.error('Error getting email templates:', error);
      return [];
    }
  }

  async getEmailTemplate(id: number): Promise<EmailTemplate | undefined> {
    try {
      const result = await this.db.select().from(emailTemplates).where(eq(emailTemplates.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting email template:', error);
      return undefined;
    }
  }

  async createEmailTemplate(template: InsertEmailTemplate): Promise<EmailTemplate> {
    try {
      const result = await this.db.insert(emailTemplates).values(template);
      const insertId = result.insertId;

      // Fetch the created template
      const createdTemplate = await this.db.select().from(emailTemplates).where(eq(emailTemplates.id, insertId)).limit(1);
      return createdTemplate[0];
    } catch (error) {
      console.error('Error creating email template:', error);
      throw error;
    }
  }

  async updateEmailTemplate(id: number, update: Partial<InsertEmailTemplate>): Promise<EmailTemplate | undefined> {
    try {
      await this.db.update(emailTemplates).set(update).where(eq(emailTemplates.id, id));

      // Fetch the updated template
      const updatedTemplate = await this.db.select().from(emailTemplates).where(eq(emailTemplates.id, id)).limit(1);
      return updatedTemplate[0];
    } catch (error) {
      console.error('Error updating email template:', error);
      return undefined;
    }
  }

  async deleteEmailTemplate(id: number): Promise<boolean> {
    try {
      const result = await this.db.delete(emailTemplates).where(eq(emailTemplates.id, id));
      return result.rowsAffected > 0;
    } catch (error) {
      console.error('Error deleting email template:', error);
      return false;
    }
  }

  async getPaypalButtons(): Promise<PaypalButton[]> {
    try {
      const result = await this.db.select().from(paypalButtons);
      return result;
    } catch (error) {
      console.error('Error getting PayPal buttons:', error);
      return [];
    }
  }

  async getPaypalButton(id: number): Promise<PaypalButton | undefined> {
    try {
      const result = await this.db.select().from(paypalButtons).where(eq(paypalButtons.id, id));
      return result[0];
    } catch (error) {
      console.error('Error getting PayPal button:', error);
      return undefined;
    }
  }

  async createPaypalButton(button: InsertPaypalButton): Promise<PaypalButton> {
    try {
      const result = await this.db.insert(paypalButtons).values(button);
      const insertId = result.insertId;

      // Fetch the created button
      const createdButton = await this.db.select().from(paypalButtons).where(eq(paypalButtons.id, insertId)).limit(1);
      return createdButton[0];
    } catch (error) {
      console.error('Error creating PayPal button:', error);
      throw error;
    }
  }

  async updatePaypalButton(id: number, update: Partial<InsertPaypalButton>): Promise<PaypalButton | undefined> {
    try {
      await this.db.update(paypalButtons).set(update).where(eq(paypalButtons.id, id));

      // Fetch the updated button
      const updatedButton = await this.db.select().from(paypalButtons).where(eq(paypalButtons.id, id)).limit(1);
      return updatedButton[0];
    } catch (error) {
      console.error('Error updating PayPal button:', error);
      return undefined;
    }
  }

  async deletePaypalButton(id: number): Promise<boolean> {
    try {
      const result = await this.db.delete(paypalButtons).where(eq(paypalButtons.id, id));
      return result.rowsAffected > 0;
    } catch (error) {
      console.error('Error deleting PayPal button:', error);
      return false;
    }
  }

  async getCustomInvoices(): Promise<CustomInvoice[]> {
    try {
      const result = await this.db.select().from(customInvoices);
      return result;
    } catch (error) {
      console.error('Error getting custom invoices:', error);
      return [];
    }
  }

  async getCustomInvoice(id: number): Promise<CustomInvoice | undefined> {
    try {
      const result = await this.db.select().from(customInvoices).where(eq(customInvoices.id, id));
      return result[0];
    } catch (error) {
      console.error('Error getting custom invoice:', error);
      return undefined;
    }
  }

  async getCustomInvoiceByNumber(invoiceNumber: string): Promise<CustomInvoice | undefined> {
    try {
      const result = await this.db.select().from(customInvoices).where(eq(customInvoices.invoiceNumber, invoiceNumber));
      return result[0];
    } catch (error) {
      console.error('Error getting custom invoice by number:', error);
      return undefined;
    }
  }

  async createCustomInvoice(invoice: InsertCustomInvoice): Promise<CustomInvoice> {
    try {
      const result = await this.db.insert(customInvoices).values(invoice);
      const insertId = result.insertId;

      // Fetch the created invoice
      const createdInvoice = await this.db.select().from(customInvoices).where(eq(customInvoices.id, insertId)).limit(1);
      return createdInvoice[0];
    } catch (error) {
      console.error('Error creating custom invoice:', error);
      throw error;
    }
  }

  async updateCustomInvoice(id: number, update: Partial<InsertCustomInvoice>): Promise<CustomInvoice | undefined> {
    try {
      await this.db.update(customInvoices).set(update).where(eq(customInvoices.id, id));

      // Fetch the updated invoice
      const updatedInvoice = await this.db.select().from(customInvoices).where(eq(customInvoices.id, id)).limit(1);
      return updatedInvoice[0];
    } catch (error) {
      console.error('Error updating custom invoice:', error);
      return undefined;
    }
  }

  async incrementCustomInvoiceViewCount(id: number): Promise<void> {
    try {
      await this.db.update(customInvoices)
        .set({ viewCount: sql`${customInvoices.viewCount} + 1` })
        .where(eq(customInvoices.id, id));
    } catch (error) {
      console.error('Error incrementing custom invoice view count:', error);
    }
  }

  async markCustomInvoiceAsPaid(id: number): Promise<CustomInvoice | undefined> {
    try {
      await this.db.update(customInvoices)
        .set({
          status: 'paid',
          paidAt: new Date().toISOString()
        })
        .where(eq(customInvoices.id, id));

      // Fetch the updated invoice
      const updatedInvoice = await this.db.select().from(customInvoices).where(eq(customInvoices.id, id)).limit(1);
      return updatedInvoice[0];
    } catch (error) {
      console.error('Error marking custom invoice as paid:', error);
      return undefined;
    }
  }

  async deleteCustomInvoice(id: number): Promise<boolean> {
    try {
      const result = await this.db.delete(customInvoices).where(eq(customInvoices.id, id));
      return result.rowsAffected > 0;
    } catch (error) {
      console.error('Error deleting custom invoice:', error);
      return false;
    }
  }

  async createContactInquiry?(inquiry: any): Promise<any> {
    // TODO: Implement contact inquiry storage in database
    console.log('Contact inquiry created:', inquiry);
    return { ...inquiry, id: Date.now() };
  }

  async getContactInquiries?(): Promise<any[]> {
    // TODO: Implement contact inquiry retrieval from database
    return [];
  }

  async updateContactInquiry?(id: number, update: any): Promise<any> {
    // TODO: Implement contact inquiry update in database
    console.log('Contact inquiry updated:', id, update);
    return { id, ...update };
  }

  async getEmbedCodes(): Promise<EmbedCode[]> {
    // TODO: Implement embed code storage in database
    return [];
  }

  async getEmbedCode(id: string): Promise<EmbedCode | undefined> {
    // TODO: Implement embed code retrieval from database
    return undefined;
  }

  async createEmbedCode(embedCode: EmbedCode): Promise<EmbedCode> {
    // TODO: Implement embed code creation in database
    console.log('Embed code created:', embedCode);
    return embedCode;
  }

  async updateEmbedCode(id: string, update: Partial<EmbedCode>): Promise<EmbedCode | undefined> {
    // TODO: Implement embed code update in database
    console.log('Embed code updated:', id, update);
    return undefined;
  }

  async deleteEmbedCode(id: string): Promise<boolean> {
    // TODO: Implement embed code deletion in database
    console.log('Embed code deleted:', id);
    return true;
  }
}

// Export the database storage instance
export const storage = new DatabaseStorage();
