import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { storage } from '../storage';
import { insertEmailTemplateSchema } from '@shared/schema';
import { DEFAULT_EMAIL_TEMPLATES, EmailTemplate, EMAIL_TEMPLATE_CATEGORIES } from '../../shared/email-templates';

export const emailTemplatesRouter = Router();

// Simple admin check middleware
const checkAdmin = (req: Request, res: Response, next: Function) => {
  console.log('Checking admin session for email templates:', req.session);
  if (req.session && req.session.isAdmin) {
    console.log('Admin session verified for email templates:', req.session.isAdmin);
    next();
  } else {
    console.log('Admin session verification failed for email templates');
    res.status(401).json({ message: 'Unauthorized' });
  }
};

// Initialize default email templates if none exist
const initializeDefaultTemplates = async () => {
  try {
    const existingTemplates = await storage.getEmailTemplates();

    if (existingTemplates.length === 0) {
      console.log('🔧 No email templates found. Creating default templates...');

      // Create essential default templates
      const defaultTemplates = [
        {
          name: 'IPTV Subscription Details',
          description: 'IPTV subscription email with credentials and installation links',
          subject: 'Your IPTV Subscription Details - {{customerName}}',
          htmlContent: '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eaeaea; border-radius: 5px;">' +
            '<div style="text-align: center; margin-bottom: 20px;">' +
            '<h1 style="color: #333; margin-bottom: 10px;">Your IPTV Subscription is Ready!</h1>' +
            '<p style="color: #666; font-size: 16px;">Order #{{orderId}}</p>' +
            '</div>' +
            '<div style="margin-bottom: 30px;">' +
            '<p style="font-size: 16px; line-height: 1.5;">Dear {{customerName}},</p>' +
            '<p style="font-size: 16px; line-height: 1.5;">Thank you for your purchase! Your IPTV subscription is now active.</p>' +
            '</div>' +
            '<div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px;">' +
            '<h3 style="color: #333; margin-bottom: 15px;">Your Subscription Details:</h3>' +
            '<p><strong>Username:</strong> {{username}}</p>' +
            '<p><strong>Password:</strong> {{password}}</p>' +
            '<p><strong>M3U URL:</strong> {{m3uUrl}}</p>' +
            '<p><strong>Portal URL:</strong> {{portalUrl}}</p>' +
            '</div>' +
            '<div style="margin-bottom: 20px;">' +
            '<h3 style="color: #333; margin-bottom: 15px;">Installation Links:</h3>' +
            '<p><strong>Android:</strong> <a href="{{androidLink}}">Download App</a></p>' +
            '<p><strong>iOS:</strong> <a href="{{iosLink}}">Download App</a></p>' +
            '<p><strong>Windows:</strong> <a href="{{windowsLink}}">Download App</a></p>' +
            '</div>' +
            '<div style="text-align: center; margin-top: 30px;">' +
            '<p style="color: #666; font-size: 14px;">If you need help, please contact our support team.</p>' +
            '</div>' +
            '</div>',
          textContent: 'Your IPTV subscription is ready! Order #{{orderId}}. Username: {{username}}, Password: {{password}}, M3U URL: {{m3uUrl}}',
          category: 'subscription',
          isDefault: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          name: 'Purchase Confirmation',
          description: 'General purchase confirmation email',
          subject: 'Purchase Confirmation - Order #{{orderId}}',
          htmlContent: '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eaeaea; border-radius: 5px;">' +
            '<div style="text-align: center; margin-bottom: 20px;">' +
            '<h1 style="color: #333; margin-bottom: 10px;">Thank You for Your Purchase!</h1>' +
            '<p style="color: #666; font-size: 16px;">Order #{{orderId}}</p>' +
            '</div>' +
            '<div style="margin-bottom: 30px;">' +
            '<p style="font-size: 16px; line-height: 1.5;">Dear {{customerName}},</p>' +
            '<p style="font-size: 16px; line-height: 1.5;">Thank you for your purchase! We\'ve received your order and it\'s being processed.</p>' +
            '</div>' +
            '<div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px;">' +
            '<h3 style="color: #333; margin-bottom: 15px;">Order Details:</h3>' +
            '<p><strong>Product:</strong> {{productName}}</p>' +
            '<p><strong>Amount:</strong> ${{amount}}</p>' +
            '<p><strong>Order Date:</strong> {{orderDate}}</p>' +
            '</div>' +
            '<div style="text-align: center; margin-top: 30px;">' +
            '<p style="color: #666; font-size: 14px;">Thank you for choosing our service!</p>' +
            '</div>' +
            '</div>',
          textContent: 'Thank you for your purchase! Order #{{orderId}}. Product: {{productName}}, Amount: ${{amount}}',
          category: 'purchase',
          isDefault: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          name: 'Trial Subscription',
          description: 'Trial subscription confirmation email',
          subject: 'Your Trial Subscription is Active - {{customerName}}',
          htmlContent: '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eaeaea; border-radius: 5px;">' +
            '<div style="text-align: center; margin-bottom: 20px;">' +
            '<h1 style="color: #333; margin-bottom: 10px;">Your Trial is Active!</h1>' +
            '<p style="color: #666; font-size: 16px;">Order #{{orderId}}</p>' +
            '</div>' +
            '<div style="margin-bottom: 30px;">' +
            '<p style="font-size: 16px; line-height: 1.5;">Dear {{customerName}},</p>' +
            '<p style="font-size: 16px; line-height: 1.5;">Welcome to your trial subscription! You now have access to all premium features.</p>' +
            '</div>' +
            '<div style="background-color: #e6f7ff; padding: 20px; border-radius: 5px; margin-bottom: 20px;">' +
            '<h3 style="color: #333; margin-bottom: 15px;">Trial Details:</h3>' +
            '<p><strong>Trial Duration:</strong> {{trialDuration}}</p>' +
            '<p><strong>Access Level:</strong> Full Premium Access</p>' +
            '<p><strong>Trial Ends:</strong> {{trialEndDate}}</p>' +
            '</div>' +
            '<div style="text-align: center; margin-top: 30px;">' +
            '<p style="color: #666; font-size: 14px;">Enjoy your trial! You can upgrade to a full subscription anytime.</p>' +
            '</div>' +
            '</div>',
          textContent: 'Your trial subscription is active! Order #{{orderId}}. Trial Duration: {{trialDuration}}',
          category: 'trial',
          isDefault: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ];

      // Create each template
      for (const template of defaultTemplates) {
        await storage.createEmailTemplate(template);
      }

      console.log(`✅ Created ${defaultTemplates.length} default email templates`);
    }
  } catch (error) {
    console.error('❌ Error initializing default email templates:', error);
  }
};

// Initialize default templates on startup
initializeDefaultTemplates();

// Get all email templates
emailTemplatesRouter.get('/', checkAdmin, async (req: Request, res: Response) => {
  try {
    const templates = await storage.getEmailTemplates();
    res.json(templates);
  } catch (error) {
    console.error('Error fetching email templates:', error);
    res.status(500).json({ message: 'Failed to fetch email templates' });
  }
});

// Get email templates by category
emailTemplatesRouter.get('/category/:category', checkAdmin, async (req: Request, res: Response) => {
  try {
    const { category } = req.params;
    const templates = await storage.getEmailTemplates();

    const filteredTemplates = templates.filter(template => template.category === category);

    res.json(filteredTemplates);
  } catch (error) {
    console.error('Error fetching email templates by category:', error);
    res.status(500).json({ message: 'Failed to fetch email templates by category' });
  }
});

// Get email template categories
emailTemplatesRouter.get('/categories', checkAdmin, async (req: Request, res: Response) => {
  try {
    res.json(EMAIL_TEMPLATE_CATEGORIES);
  } catch (error) {
    console.error('Error fetching email template categories:', error);
    res.status(500).json({ message: 'Failed to fetch email template categories' });
  }
});

// Get a specific email template
emailTemplatesRouter.get('/:id', checkAdmin, async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({ message: 'Invalid template ID' });
    }

    const template = await storage.getEmailTemplate(id);
    if (!template) {
      return res.status(404).json({ message: 'Template not found' });
    }

    res.json(template);
  } catch (error) {
    console.error('Error fetching email template:', error);
    res.status(500).json({ message: 'Failed to fetch email template' });
  }
});

// Create a new email template
emailTemplatesRouter.post('/', checkAdmin, async (req: Request, res: Response) => {
  try {
    // Validate the request body
    const templateData = {
      ...req.body,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    const validatedData = insertEmailTemplateSchema.parse(templateData);

    // Create the template
    const template = await storage.createEmailTemplate(validatedData);

    res.status(201).json(template);
  } catch (error) {
    console.error('Error creating email template:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({ message: 'Failed to create email template' });
  }
});

// Update an email template
emailTemplatesRouter.put('/:id', checkAdmin, async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({ message: 'Invalid template ID' });
    }

    // Check if the template exists
    const existingTemplate = await storage.getEmailTemplate(id);
    if (!existingTemplate) {
      return res.status(404).json({ message: 'Template not found' });
    }

    // Update the template
    const updateData = {
      ...req.body,
      updatedAt: new Date().toISOString()
    };

    const updatedTemplate = await storage.updateEmailTemplate(id, updateData);

    res.json(updatedTemplate);
  } catch (error) {
    console.error('Error updating email template:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({ message: 'Failed to update email template' });
  }
});

// Clear all non-default email templates (must be before /:id route)
emailTemplatesRouter.delete('/clear/all', checkAdmin, async (req: Request, res: Response) => {
  try {
    const allTemplates = await storage.getEmailTemplates();
    const nonDefaultTemplates = allTemplates.filter(template => !template.isDefault);

    let deletedCount = 0;
    for (const template of nonDefaultTemplates) {
      const deleted = await storage.deleteEmailTemplate(template.id as number);
      if (deleted) {
        deletedCount++;
      }
    }

    res.json({
      message: `Successfully deleted ${deletedCount} email templates`,
      deletedCount,
      totalTemplates: nonDefaultTemplates.length
    });
  } catch (error) {
    console.error('Error clearing email templates:', error);
    res.status(500).json({ message: 'Failed to clear email templates' });
  }
});

// Force clear ALL email templates (including defaults) - for admin use (must be before /:id route)
emailTemplatesRouter.delete('/force-clear/all', checkAdmin, async (req: Request, res: Response) => {
  try {
    const allTemplates = await storage.getEmailTemplates();

    let deletedCount = 0;
    for (const template of allTemplates) {
      const deleted = await storage.deleteEmailTemplate(template.id as number);
      if (deleted) {
        deletedCount++;
      }
    }

    res.json({
      message: `Successfully deleted ALL ${deletedCount} email templates`,
      deletedCount,
      totalTemplates: allTemplates.length
    });
  } catch (error) {
    console.error('Error force clearing email templates:', error);
    res.status(500).json({ message: 'Failed to force clear email templates' });
  }
});

// Delete an email template (must be after specific routes)
emailTemplatesRouter.delete('/:id', checkAdmin, async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({ message: 'Invalid template ID' });
    }

    // Check if the template exists
    const existingTemplate = await storage.getEmailTemplate(id);
    if (!existingTemplate) {
      return res.status(404).json({ message: 'Template not found' });
    }

    // Allow deleting any template now (including defaults)
    // if (existingTemplate.isDefault) {
    //   return res.status(400).json({ message: 'Cannot delete default email template' });
    // }

    // Delete the template
    const success = await storage.deleteEmailTemplate(id);

    if (success) {
      res.json({ message: 'Template deleted successfully' });
    } else {
      res.status(500).json({ message: 'Failed to delete template' });
    }
  } catch (error) {
    console.error('Error deleting email template:', error);
    res.status(500).json({ message: 'Failed to delete email template' });
  }
});

// Preview an email template with test data
emailTemplatesRouter.post('/preview', checkAdmin, async (req: Request, res: Response) => {
  try {
    const { templateId, testData } = req.body;

    if (!templateId) {
      return res.status(400).json({ message: 'Template ID is required' });
    }

    // Find the template
    const template = await storage.getEmailTemplate(parseInt(templateId));

    if (!template) {
      return res.status(404).json({ message: 'Email template not found' });
    }

    // Replace placeholders with test data
    let htmlContent = template.htmlContent;
    let textContent = template.textContent || '';
    let subject = template.subject;

    // Simple placeholder replacement
    if (testData) {
      Object.keys(testData).forEach(key => {
        const regex = new RegExp(`{{${key}}}`, 'g');
        htmlContent = htmlContent.replace(regex, testData[key]);
        textContent = textContent.replace(regex, testData[key]);
        subject = subject.replace(regex, testData[key]);
      });
    }

    res.json({
      subject,
      htmlContent,
      textContent
    });
  } catch (error) {
    console.error('Error previewing email template:', error);
    res.status(500).json({ message: 'Failed to preview email template' });
  }
});
