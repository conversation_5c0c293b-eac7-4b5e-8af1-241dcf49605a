import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import {
  Bold,
  Italic,
  Underline,
  Link as LinkIcon,
  AlignLeft,
  AlignCenter,
  AlignRight,
  List,
  ListOrdered,
  Type,
  Image,
  Code,
  Undo,
  Redo,
} from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface EmailEditorProps {
  initialContent: string;
  onChange: (content: string) => void;
}

export default function EmailEditor({ initialContent, onChange }: EmailEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const [linkUrl, setLinkUrl] = useState('');
  const [linkText, setLinkText] = useState('');
  const [isLinkPopoverOpen, setIsLinkPopoverOpen] = useState(false);
  const [imageUrl, setImageUrl] = useState('');
  const [imageAlt, setImageAlt] = useState('');
  const [isImagePopoverOpen, setIsImagePopoverOpen] = useState(false);

  // Initialize editor with content
  useEffect(() => {
    if (editorRef.current) {
      editorRef.current.innerHTML = initialContent;
    }
  }, [initialContent]);

  // Update content when editor changes
  const handleContentChange = () => {
    if (editorRef.current) {
      onChange(editorRef.current.innerHTML);
    }
  };

  // Execute command on the document
  const execCommand = (command: string, value: string = '') => {
    document.execCommand(command, false, value);
    handleContentChange();
    editorRef.current?.focus();
  };

  // Handle formatting commands
  const handleFormat = (command: string) => {
    execCommand(command);
  };

  // Handle link insertion
  const handleInsertLink = () => {
    if (linkUrl && linkText) {
      const linkHtml = `<a href="${linkUrl}" target="_blank" rel="noopener noreferrer">${linkText}</a>`;
      execCommand('insertHTML', linkHtml);
      setLinkUrl('');
      setLinkText('');
      setIsLinkPopoverOpen(false);
    }
  };

  // Handle image insertion
  const handleInsertImage = () => {
    if (imageUrl) {
      const imgHtml = `<img src="${imageUrl}" alt="${imageAlt}" style="max-width: 100%;" />`;
      execCommand('insertHTML', imgHtml);
      setImageUrl('');
      setImageAlt('');
      setIsImagePopoverOpen(false);
    }
  };

  // Handle font size change
  const handleFontSize = (size: string) => {
    execCommand('fontSize', size);
  };

  // Toolbar button component
  const ToolbarButton = ({ icon, command, tooltip }: { icon: React.ReactNode, command: string, tooltip: string }) => (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8"
            onClick={() => handleFormat(command)}
          >
            {icon}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>{tooltip}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );

  return (
    <div className="border-0 rounded-md overflow-hidden">
      <div className="bg-muted p-2 flex flex-wrap gap-1 border-b">
        <ToolbarButton icon={<Bold className="h-4 w-4" />} command="bold" tooltip="Bold" />
        <ToolbarButton icon={<Italic className="h-4 w-4" />} command="italic" tooltip="Italic" />
        <ToolbarButton icon={<Underline className="h-4 w-4" />} command="underline" tooltip="Underline" />

        <div className="w-px h-8 bg-border mx-1" />

        <ToolbarButton icon={<AlignLeft className="h-4 w-4" />} command="justifyLeft" tooltip="Align Left" />
        <ToolbarButton icon={<AlignCenter className="h-4 w-4" />} command="justifyCenter" tooltip="Align Center" />
        <ToolbarButton icon={<AlignRight className="h-4 w-4" />} command="justifyRight" tooltip="Align Right" />

        <div className="w-px h-8 bg-border mx-1" />

        <ToolbarButton icon={<List className="h-4 w-4" />} command="insertUnorderedList" tooltip="Bullet List" />
        <ToolbarButton icon={<ListOrdered className="h-4 w-4" />} command="insertOrderedList" tooltip="Numbered List" />

        <div className="w-px h-8 bg-border mx-1" />

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Select onValueChange={handleFontSize}>
                <SelectTrigger className="h-8 w-[110px]">
                  <SelectValue placeholder="Font Size" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">Small</SelectItem>
                  <SelectItem value="3">Normal</SelectItem>
                  <SelectItem value="5">Large</SelectItem>
                  <SelectItem value="7">Huge</SelectItem>
                </SelectContent>
              </Select>
            </TooltipTrigger>
            <TooltipContent>
              <p>Font Size</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <div className="w-px h-8 bg-border mx-1" />

        <Popover open={isLinkPopoverOpen} onOpenChange={setIsLinkPopoverOpen}>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <PopoverTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <LinkIcon className="h-4 w-4" />
                  </Button>
                </PopoverTrigger>
              </TooltipTrigger>
              <TooltipContent>
                <p>Insert Link</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <PopoverContent className="w-80">
            <div className="grid gap-4">
              <div className="space-y-2">
                <h4 className="font-medium leading-none">Insert Link</h4>
                <p className="text-sm text-muted-foreground">
                  Add a hyperlink to your email
                </p>
              </div>
              <div className="grid gap-2">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="linkText" className="text-right">
                    Text
                  </Label>
                  <Input
                    id="linkText"
                    value={linkText}
                    onChange={(e) => setLinkText(e.target.value)}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="linkUrl" className="text-right">
                    URL
                  </Label>
                  <Input
                    id="linkUrl"
                    value={linkUrl}
                    onChange={(e) => setLinkUrl(e.target.value)}
                    className="col-span-3"
                  />
                </div>
              </div>
              <Button onClick={handleInsertLink} disabled={!linkUrl || !linkText}>
                Insert Link
              </Button>
            </div>
          </PopoverContent>
        </Popover>

        <Popover open={isImagePopoverOpen} onOpenChange={setIsImagePopoverOpen}>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <PopoverTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <Image className="h-4 w-4" />
                  </Button>
                </PopoverTrigger>
              </TooltipTrigger>
              <TooltipContent>
                <p>Insert Image</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <PopoverContent className="w-80">
            <div className="grid gap-4">
              <div className="space-y-2">
                <h4 className="font-medium leading-none">Insert Image</h4>
                <p className="text-sm text-muted-foreground">
                  Add an image to your email
                </p>
              </div>
              <div className="grid gap-2">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="imageUrl" className="text-right">
                    URL
                  </Label>
                  <Input
                    id="imageUrl"
                    value={imageUrl}
                    onChange={(e) => setImageUrl(e.target.value)}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="imageAlt" className="text-right">
                    Alt Text
                  </Label>
                  <Input
                    id="imageAlt"
                    value={imageAlt}
                    onChange={(e) => setImageAlt(e.target.value)}
                    className="col-span-3"
                  />
                </div>
              </div>
              <Button onClick={handleInsertImage} disabled={!imageUrl}>
                Insert Image
              </Button>
            </div>
          </PopoverContent>
        </Popover>

        <div className="w-px h-8 bg-border mx-1" />

        <ToolbarButton icon={<Code className="h-4 w-4" />} command="formatBlock" tooltip="Code Block" />

        <div className="w-px h-8 bg-border mx-1" />

        <ToolbarButton icon={<Undo className="h-4 w-4" />} command="undo" tooltip="Undo" />
        <ToolbarButton icon={<Redo className="h-4 w-4" />} command="redo" tooltip="Redo" />
      </div>

      <div
        ref={editorRef}
        className="min-h-[300px] p-4 focus:outline-none"
        contentEditable
        onInput={handleContentChange}
        onBlur={handleContentChange}
      />
    </div>
  );
}
