import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { useLocation, useRoute } from 'wouter';
import AdminLayout from '@/components/admin/AdminLayout';
import ImageUploader from '@/components/admin/ImageUploader';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectSeparator,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import SimpleRichTextEditor from '@/components/admin/SimpleRichTextEditor';
import { Separator } from '@/components/ui/separator';
import { ArrowLeft, AlertTriangle, Loader2 } from 'lucide-react';

// Form schema for custom checkout page
const formSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  slug: z.string().optional(),
  productName: z.string().min(1, 'Product name is required'),
  productDescription: z.string().min(1, 'Product description is required'),
  price: z.coerce.number().positive('Price must be positive'),
  imageUrl: z.string().optional(),
  paymentMethod: z.enum(['paypal', 'custom-link', 'paypal-button-embed', 'trial-custom-link', 'trial-paypal-button-embed', 'embed-code']),
  customPaymentLinkId: z.string().optional(),
  paypalButtonId: z.string().optional(),
  trialCustomPaymentLinkId: z.string().optional(),
  trialPaypalButtonId: z.string().optional(),
  embedCodeId: z.string().optional(),
  confirmationMessage: z.string().optional(),
  headerTitle: z.string().optional(),
  footerText: z.string().optional(),
  headerLogo: z.string().optional(),
  footerLogo: z.string().optional(),
  themeMode: z.enum(['light', 'dark']).default('light'),
  useReferrerMasking: z.boolean().default(false),
  redirectDelay: z.coerce.number().min(0).max(10000).default(2000),
  smtpProviderId: z.string().optional(),
  requireAllowedEmail: z.boolean().default(false),
  isTrialCheckout: z.boolean().default(false),

  active: z.boolean().default(true)
});

type FormValues = z.infer<typeof formSchema>;

export default function EditCustomCheckoutPage() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [, setLocation] = useLocation();
  const [match, params] = useRoute('/admin/custom-checkout/edit/:id');

  const pageId = params?.id;

  // Query to fetch the specific checkout page
  const { data: checkoutPage, isLoading: isLoadingPage } = useQuery({
    queryKey: ['/api/custom-checkout', pageId],
    queryFn: () => apiRequest(`/api/custom-checkout/${pageId}`, 'GET'),
    enabled: !!pageId
  });

  // Query to fetch payment config (for payment methods)
  const { data: paymentConfig } = useQuery({
    queryKey: ['/api/admin/payment-config'],
    queryFn: () => apiRequest('/api/admin/payment-config', 'GET')
  });

  // Query to fetch email config (for SMTP providers)
  const { data: emailConfig } = useQuery({
    queryKey: ['/api/admin/email-config'],
    queryFn: () => apiRequest('/api/admin/email-config', 'GET')
  });

  // Query to fetch embed codes
  const { data: embedCodes = [] } = useQuery({
    queryKey: ['/api/embed-codes'],
    queryFn: () => apiRequest('/api/embed-codes', 'GET')
  });

  // Form for editing custom checkout page
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: '',
      slug: '',
      productName: '',
      productDescription: '',
      price: 0,
      imageUrl: '',
      paymentMethod: 'paypal',
      customPaymentLinkId: '',
      paypalButtonId: '',
      trialCustomPaymentLinkId: '',
      trialPaypalButtonId: '',
      confirmationMessage: '<div class="space-y-3"><p><strong>🛒 Ready to complete your purchase?</strong></p><p>✅ <strong>What you\'re getting:</strong></p><ul class="list-disc list-inside space-y-1"><li>Instant access to your product</li><li>Full customer support</li><li>Secure payment processing</li></ul><p class="text-sm text-muted-foreground mt-3">💳 <em>Your payment will be processed securely</em></p><p class="text-xs text-muted-foreground">By proceeding, you agree to our terms of service and privacy policy.</p></div>',
      headerTitle: '',
      footerText: '',
      headerLogo: '',
      footerLogo: '',
      themeMode: 'light',
      useReferrerMasking: false,
      redirectDelay: 2000,
      smtpProviderId: 'default',
      requireAllowedEmail: false,
      isTrialCheckout: false,
      active: true
    }
  });

  // Update form when checkout page data is loaded
  useEffect(() => {
    if (checkoutPage) {
      form.reset({
        ...checkoutPage,
        price: Number(checkoutPage.price),
        headerTitle: checkoutPage.headerTitle || '',
        footerText: checkoutPage.footerText || '',
        headerLogo: checkoutPage.headerLogo || '',
        footerLogo: checkoutPage.footerLogo || '',
        themeMode: checkoutPage.themeMode || 'light',
        useReferrerMasking: checkoutPage.useReferrerMasking || false,
        redirectDelay: checkoutPage.redirectDelay || 2000,
        requireAllowedEmail: checkoutPage.requireAllowedEmail || false,
        isTrialCheckout: checkoutPage.isTrialCheckout || false,
        active: checkoutPage.active !== undefined ? checkoutPage.active : true,
        smtpProviderId: checkoutPage.smtpProviderId || 'default',
        customPaymentLinkId: checkoutPage.customPaymentLinkId || '',
        paypalButtonId: checkoutPage.paypalButtonId || '',
        trialCustomPaymentLinkId: checkoutPage.trialCustomPaymentLinkId || '',
        trialPaypalButtonId: checkoutPage.trialPaypalButtonId || '',
        embedCodeId: checkoutPage.embedCodeId || '',
      });
    }
  }, [checkoutPage, form]);

  // Get available payment methods
  const paypalProvider = paymentConfig?.providers?.find((p: any) => p.id === 'paypal');
  const customLinkProvider = paymentConfig?.providers?.find((p: any) => p.id === 'custom-link');
  const paypalButtonEmbedProvider = paymentConfig?.providers?.find((p: any) => p.id === 'paypal-button-embed');
  const trialCustomLinkProvider = paymentConfig?.providers?.find((p: any) => p.id === 'trial-custom-link');
  const trialPaypalButtonEmbedProvider = paymentConfig?.providers?.find((p: any) => p.id === 'trial-paypal-button-embed');

  const customLinks = customLinkProvider?.config?.links || [];
  const paypalButtons = paypalButtonEmbedProvider?.config?.buttons || [];
  const trialCustomLinks = trialCustomLinkProvider?.config?.links || [];
  const trialPaypalButtons = trialPaypalButtonEmbedProvider?.config?.buttons || [];

  // Get available SMTP providers
  const smtpProviders = emailConfig?.providers?.filter((p: any) => p.active) || [];

  // Mutation to update the custom checkout page
  const updateMutation = useMutation({
    mutationFn: (data: FormValues) => {
      console.log('Updating custom checkout page with data:', data);
      return apiRequest(`/api/custom-checkout/${pageId}`, 'PUT', data);
    },
    onSuccess: async (updatedPage) => {
      console.log('Custom checkout page updated successfully:', updatedPage);

      // Invalidate queries first to trigger refetch
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ['/api/custom-checkout'] }),
        queryClient.invalidateQueries({ queryKey: ['/api/custom-checkout', pageId] })
      ]);

      // Small delay to ensure the refetch completes before form reset
      await new Promise(resolve => setTimeout(resolve, 100));

      // Update the form with the response data to prevent field reset
      if (updatedPage) {
        form.reset({
          ...updatedPage,
          price: Number(updatedPage.price),
          headerTitle: updatedPage.headerTitle || '',
          footerText: updatedPage.footerText || '',
          headerLogo: updatedPage.headerLogo || '',
          footerLogo: updatedPage.footerLogo || '',
          themeMode: updatedPage.themeMode || 'light',
          useReferrerMasking: updatedPage.useReferrerMasking || false,
          redirectDelay: updatedPage.redirectDelay || 2000,
          requireAllowedEmail: updatedPage.requireAllowedEmail || false,
          isTrialCheckout: updatedPage.isTrialCheckout || false,
          active: updatedPage.active !== undefined ? updatedPage.active : true,
          smtpProviderId: updatedPage.smtpProviderId || 'default',
          customPaymentLinkId: updatedPage.customPaymentLinkId || '',
          paypalButtonId: updatedPage.paypalButtonId || '',
          trialCustomPaymentLinkId: updatedPage.trialCustomPaymentLinkId || '',
          trialPaypalButtonId: updatedPage.trialPaypalButtonId || '',
          embedCodeId: updatedPage.embedCodeId || '',
        });
      }

      toast({
        title: 'Success',
        description: 'Custom checkout page updated successfully',
      });
    },
    onError: (error: any) => {
      console.error('Error updating custom checkout page:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to update custom checkout page',
        variant: 'destructive',
      });
    }
  });

  // Handle form submission for updating the page
  const onSubmit = (values: FormValues) => {
    console.log('🔥 FORM SUBMISSION TRIGGERED');
    console.log('🔥 Form values:', values);
    console.log('🔥 Form errors:', form.formState.errors);
    console.log('🔥 Form is valid:', form.formState.isValid);
    console.log('🔥 Page ID:', pageId);

    if (!form.formState.isValid) {
      console.error('🚨 Form validation failed:', form.formState.errors);
      return;
    }

    console.log('✅ Form is valid, calling mutation...');
    updateMutation.mutate(values);
  };

  // Show loading state while fetching page data
  if (isLoadingPage) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="flex items-center gap-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>Loading checkout page...</span>
          </div>
        </div>
      </AdminLayout>
    );
  }

  // Show error if page not found
  if (!checkoutPage && !isLoadingPage) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-2">Checkout Page Not Found</h2>
            <p className="text-muted-foreground mb-4">The checkout page you're looking for doesn't exist.</p>
            <Button onClick={() => setLocation('/admin/custom-checkout')}>
              <ArrowLeft className="mr-2 h-4 w-4" /> Back to Checkout Pages
            </Button>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="flex items-center gap-4 mb-6">
        <Button variant="outline" size="sm" onClick={() => setLocation('/admin/custom-checkout')}>
          <ArrowLeft className="mr-2 h-4 w-4" /> Back to Checkout Pages
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Edit Custom Checkout Page</h1>
          <p className="text-muted-foreground">Update your checkout page details and settings</p>
        </div>
      </div>

      <Card className="max-w-4xl">
        <CardHeader>
          <CardTitle>Edit: {checkoutPage?.title}</CardTitle>
          <CardDescription>
            Update the details and settings for your custom checkout page
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form
              onSubmit={(e) => {
                console.log('🔥 FORM SUBMIT EVENT TRIGGERED!', e);
                e.preventDefault();
                form.handleSubmit(onSubmit)(e);
              }}
              className="space-y-6"
            >
              <Tabs defaultValue="basic" className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="basic">Basic Info</TabsTrigger>
                  <TabsTrigger value="product">Product Details</TabsTrigger>
                  <TabsTrigger value="payment">Payment Settings</TabsTrigger>
                  <TabsTrigger value="confirmation">Confirmation</TabsTrigger>
                </TabsList>

                <TabsContent value="basic" className="space-y-4 pt-6">
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Page Title</FormLabel>
                        <FormControl>
                          <Input placeholder="Summer Sale Offer" {...field} />
                        </FormControl>
                        <FormDescription>
                          A title for your checkout page (not visible to customers)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="slug"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>URL Slug</FormLabel>
                        <FormControl>
                          <div className="flex items-center">
                            <span className="px-3 py-2 bg-muted border border-r-0 rounded-l-md text-sm text-muted-foreground">
                              /checkout/
                            </span>
                            <Input
                              placeholder="summer-sale"
                              {...field}
                              className="rounded-l-none"
                            />
                          </div>
                        </FormControl>
                        <FormDescription>
                          Custom URL path for this checkout page. Be careful when changing this as it will affect existing links.
                          <br />
                          <strong>Full URL:</strong> {window.location.origin}/checkout/{field.value || 'your-slug'}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="headerTitle"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Header Title (Optional)</FormLabel>
                          <FormControl>
                            <Input placeholder="My Store" {...field} />
                          </FormControl>
                          <FormDescription>
                            Custom title to display in the checkout page header. Leave empty to use default.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="footerText"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Footer Text (Optional)</FormLabel>
                          <FormControl>
                            <Input placeholder="© 2024 My Store. All rights reserved." {...field} />
                          </FormControl>
                          <FormDescription>
                            Custom footer text to display at the bottom of the checkout page.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="headerLogo"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Header Logo (Optional)</FormLabel>
                          <FormControl>
                            <div className="space-y-2">
                              <ImageUploader
                                initialUrl={field.value}
                                onImageUploaded={(url) => field.onChange(url)}
                              />
                            </div>
                          </FormControl>
                          <FormDescription>
                            Upload a logo for the checkout page header. This will replace the header title when present.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="footerLogo"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Footer Logo (Optional)</FormLabel>
                          <FormControl>
                            <div className="space-y-2">
                              <ImageUploader
                                initialUrl={field.value}
                                onImageUploaded={(url) => field.onChange(url)}
                              />
                            </div>
                          </FormControl>
                          <FormDescription>
                            Upload a logo for the checkout page footer. This will appear above the footer text.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="themeMode"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Theme Mode</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select theme mode" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="light">Light Mode</SelectItem>
                            <SelectItem value="dark">Dark Mode</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Choose the theme mode for this checkout page. This only affects the checkout page, not the admin panel.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Simplified Redirect Section */}
                  <div className="space-y-6 mt-8">
                    <div>
                      <h3 className="text-lg font-medium">Redirect Link</h3>
                      <p className="text-sm text-muted-foreground">
                        Generate a redirect link with delay to hide the traffic source.
                      </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        control={form.control}
                        name="useReferrerMasking"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">
                                Enable Redirect Link
                              </FormLabel>
                              <FormDescription>
                                Generate a redirect link with delay to hide traffic source
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="redirectDelay"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Redirect Delay (milliseconds)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="0"
                                max="10000"
                                step="500"
                                {...field}
                                onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                              />
                            </FormControl>
                            <FormDescription>
                              How long to show the loading page before redirecting (0-10000ms)
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Redirect Link Generation */}
                    {form.watch('useReferrerMasking') && (
                      <div className="mt-6 p-4 border rounded-lg bg-muted/50">
                        <h4 className="font-medium mb-2">Your Short Redirect Links</h4>
                        <p className="text-sm text-muted-foreground mb-3">
                          Use these short links instead of the direct checkout URL to hide the traffic source:
                        </p>

                        {/* Short Link */}
                        <div className="space-y-3">
                          <div>
                            <Label className="text-sm font-medium">Short Link (Recommended)</Label>
                            <div className="flex items-center gap-2 mt-1">
                              <Input
                                readOnly
                                value={`${window.location.origin}/r/${form.watch('slug')}`}
                                className="font-mono text-xs"
                              />
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  const shortUrl = `${window.location.origin}/r/${form.watch('slug')}`;
                                  navigator.clipboard.writeText(shortUrl);
                                  toast({
                                    title: 'Copied!',
                                    description: 'Short redirect link copied to clipboard',
                                  });
                                }}
                              >
                                Copy
                              </Button>
                            </div>
                          </div>

                          {/* Custom Delay Link */}
                          {form.watch('redirectDelay') !== 2000 && (
                            <div>
                              <Label className="text-sm font-medium">With Custom Delay ({form.watch('redirectDelay')}ms)</Label>
                              <div className="flex items-center gap-2 mt-1">
                                <Input
                                  readOnly
                                  value={`${window.location.origin}/r/${form.watch('slug')}-${form.watch('redirectDelay')}`}
                                  className="font-mono text-xs"
                                />
                                <Button
                                  type="button"
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    const customUrl = `${window.location.origin}/r/${form.watch('slug')}-${form.watch('redirectDelay')}`;
                                    navigator.clipboard.writeText(customUrl);
                                    toast({
                                      title: 'Copied!',
                                      description: 'Custom delay redirect link copied to clipboard',
                                    });
                                  }}
                                >
                                  Copy
                                </Button>
                              </div>
                            </div>
                          )}
                        </div>

                        <div className="mt-3 p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
                          <p className="text-xs text-blue-700 dark:text-blue-300">
                            <strong>How it works:</strong> Short links like <code>/r/{form.watch('slug')}</code> will show a loading page for {form.watch('redirectDelay')}ms before redirecting with Facebook as the referrer.
                          </p>
                        </div>
                      </div>
                    )}
                  </div>

                </TabsContent>

                <TabsContent value="product" className="space-y-4 pt-6">
                  <FormField
                    control={form.control}
                    name="productName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Product Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Premium WordPress Theme" {...field} />
                        </FormControl>
                        <FormDescription>
                          The name of the product you're selling
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="productDescription"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Product Description</FormLabel>
                        <FormControl>
                          <SimpleRichTextEditor
                            initialValue={field.value}
                            onChange={field.onChange}
                            height={200}
                            placeholder="A professionally designed WordPress theme with premium features..."
                            showPreview={false}
                          />
                        </FormControl>
                        <FormDescription>
                          Describe the product in detail. You can use formatting to make it more attractive.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="price"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Price</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="0.01"
                            step="0.01"
                            placeholder="49.99"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          The price of the product in USD
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="imageUrl"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Product Image (Optional)</FormLabel>
                        <FormControl>
                          <div className="space-y-2">
                            <ImageUploader
                              initialUrl={field.value}
                              onImageUploaded={(url) => field.onChange(url)}
                            />
                          </div>
                        </FormControl>
                        <FormDescription>
                          Upload an image for your product
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />


                </TabsContent>

                <TabsContent value="payment" className="space-y-4 pt-6">
                  <FormField
                    control={form.control}
                    name="paymentMethod"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Payment Method</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select payment method" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectGroup>
                              <SelectLabel>Regular Payment Methods</SelectLabel>
                              {paypalProvider && (
                                <SelectItem value="paypal">
                                  PayPal Invoice {!paypalProvider.active && " (Inactive)"}
                                </SelectItem>
                              )}
                              {customLinkProvider && (
                                <SelectItem value="custom-link">
                                  Custom Payment Link {!customLinkProvider.active && " (Inactive)"}
                                </SelectItem>
                              )}
                              {paypalButtonEmbedProvider && (
                                <SelectItem value="paypal-button-embed">
                                  PayPal Button Embed {!paypalButtonEmbedProvider.active && " (Inactive)"}
                                </SelectItem>
                              )}
                              <SelectItem value="embed-code">
                                External Embed Code (BillGang, SellPass, etc.)
                              </SelectItem>
                            </SelectGroup>
                            <SelectSeparator />
                            <SelectGroup>
                              <SelectLabel>Trial Payment Methods</SelectLabel>
                              {customLinkProvider && (
                                <SelectItem value="trial-custom-link">
                                  Trial Custom Payment Link {!customLinkProvider.active && " (Inactive)"}
                                </SelectItem>
                              )}
                              {paypalButtonEmbedProvider && (
                                <SelectItem value="trial-paypal-button-embed">
                                  Trial PayPal Button Embed {!paypalButtonEmbedProvider.active && " (Inactive)"}
                                </SelectItem>
                              )}
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Choose how customers will pay for this product
                        </FormDescription>
                        {field.value === 'paypal' && paypalProvider && !paypalProvider.active && (
                          <div className="mt-2 text-amber-600 text-sm flex items-center gap-1">
                            <AlertTriangle className="h-4 w-4" />
                            <span>PayPal is currently inactive. Activate it in Payment Settings.</span>
                          </div>
                        )}
                        {field.value === 'custom-link' && customLinkProvider && !customLinkProvider.active && (
                          <div className="mt-2 text-amber-600 text-sm flex items-center gap-1">
                            <AlertTriangle className="h-4 w-4" />
                            <span>Custom Payment Link is currently inactive. Activate it in Payment Settings.</span>
                          </div>
                        )}
                        {field.value === 'paypal-button-embed' && paypalButtonEmbedProvider && !paypalButtonEmbedProvider.active && (
                          <div className="mt-2 text-amber-600 text-sm flex items-center gap-1">
                            <AlertTriangle className="h-4 w-4" />
                            <span>PayPal Button Embed is currently inactive. Activate it in Payment Settings.</span>
                          </div>
                        )}
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {form.watch('paymentMethod') === 'custom-link' && customLinks.length > 0 && (
                    <FormField
                      control={form.control}
                      name="customPaymentLinkId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Custom Payment Link</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a payment link" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {customLinks.map((link: any) => (
                                <SelectItem key={link.id} value={link.id}>
                                  {link.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Choose a specific payment link to use (optional)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  {form.watch('paymentMethod') === 'paypal-button-embed' && paypalButtons.length > 0 && (
                    <FormField
                      control={form.control}
                      name="paypalButtonId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>PayPal Button</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a PayPal button" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {paypalButtons.map((button: any) => (
                                <SelectItem key={button.id} value={button.id}>
                                  {button.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Choose a specific PayPal button to use (optional)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  {form.watch('paymentMethod') === 'trial-custom-link' && trialCustomLinks.length > 0 && (
                    <FormField
                      control={form.control}
                      name="trialCustomPaymentLinkId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Trial Custom Payment Link</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a trial payment link" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {trialCustomLinks.map((link: any) => (
                                <SelectItem key={link.id} value={link.id}>
                                  {link.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Choose a specific trial payment link to use (optional)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  {form.watch('paymentMethod') === 'trial-paypal-button-embed' && trialPaypalButtons.length > 0 && (
                    <FormField
                      control={form.control}
                      name="trialPaypalButtonId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Trial PayPal Button</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a trial PayPal button" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {trialPaypalButtons.map((button: any) => (
                                <SelectItem key={button.id} value={button.id}>
                                  {button.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Choose a specific trial PayPal button to use (optional)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  {form.watch('paymentMethod') === 'embed-code' && embedCodes.length > 0 && (
                    <FormField
                      control={form.control}
                      name="embedCodeId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>External Embed Code</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select an embed code" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {embedCodes.filter((code: any) => code.active).map((code: any) => (
                                <SelectItem key={code.id} value={code.id}>
                                  {code.name} ({code.platform})
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Choose an external payment embed code (BillGang, SellPass, etc.)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  {form.watch('paymentMethod') === 'embed-code' && embedCodes.length === 0 && (
                    <div className="mt-2 text-amber-600 text-sm flex items-center gap-1">
                      <AlertTriangle className="h-4 w-4" />
                      <span>No embed codes configured. Please add embed codes in Payment Settings first.</span>
                    </div>
                  )}

                  <Separator className="my-4" />

                  <div className="mb-2">
                    <h3 className="text-lg font-medium">Checkout Settings</h3>
                    <p className="text-sm text-muted-foreground">
                      Configure checkout behavior and restrictions
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="requireAllowedEmail"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">
                              Require Allowed Email
                            </FormLabel>
                            <FormDescription>
                              Only allow emails from the allowed emails list to checkout
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="isTrialCheckout"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">
                              Trial Checkout
                            </FormLabel>
                            <FormDescription>
                              Mark this as a trial checkout page
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="mt-4">
                    <FormField
                      control={form.control}
                      name="active"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">
                              Active
                            </FormLabel>
                            <FormDescription>
                              Enable or disable this checkout page
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>

                  <Separator className="my-4" />

                  <div className="mb-2">
                    <h3 className="text-lg font-medium">Email Settings</h3>
                    <p className="text-sm text-muted-foreground">
                      Configure which SMTP provider to use for sending emails from this checkout page
                    </p>
                  </div>

                  <FormField
                    control={form.control}
                    name="smtpProviderId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>SMTP Provider</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select SMTP provider (optional)" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="default">Use default SMTP provider</SelectItem>
                            {smtpProviders.map((provider: any) => (
                              <SelectItem key={provider.id} value={provider.id}>
                                {provider.name} {provider.isDefault && "(Default)"} {provider.isBackup && "(Backup)"}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Choose a specific SMTP provider to use for sending emails from this checkout page.
                          If not selected, the default SMTP provider will be used.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </TabsContent>

                <TabsContent value="confirmation" className="space-y-4 pt-6">
                  <FormField
                    control={form.control}
                    name="confirmationMessage"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Confirmation Message</FormLabel>
                        <FormControl>
                          <SimpleRichTextEditor
                            initialValue={field.value}
                            onChange={field.onChange}
                            height={300}
                            placeholder="Enter a message to show in the confirmation popup before purchase"
                            showPreview={false}
                          />
                        </FormControl>
                        <FormDescription>
                          This message will be shown to the customer in a confirmation dialog before they complete their purchase.
                          You can use HTML formatting to make it more attractive. The default message includes emojis and structured content.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="bg-muted/50 p-4 rounded-lg">
                    <h4 className="font-medium mb-2">💡 Tips for effective confirmation messages:</h4>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• Use emojis to make the message more engaging</li>
                      <li>• Clearly list what the customer will receive</li>
                      <li>• Include security and trust indicators</li>
                      <li>• Keep it concise but informative</li>
                      <li>• Use HTML formatting for better presentation</li>
                    </ul>
                  </div>
                </TabsContent>
              </Tabs>

              <div className="flex justify-end gap-4 pt-6 border-t">
                <Button type="button" variant="outline" onClick={() => setLocation('/admin/custom-checkout')}>
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={updateMutation.isPending}
                  onClick={async () => {
                    console.log('🔥 SAVE BUTTON CLICKED!');
                    console.log('🔥 Form state:', form.formState);
                    console.log('🔥 Form values:', form.getValues());
                    console.log('🔥 Form errors:', form.formState.errors);
                    console.log('🔥 Form is valid:', form.formState.isValid);

                    // Trigger validation manually
                    const isValid = await form.trigger();
                    console.log('🔥 Manual validation result:', isValid);

                    if (!isValid) {
                      console.error('🚨 Form validation failed!');
                      console.error('🚨 Validation errors:', form.formState.errors);
                    }
                  }}
                >
                  {updateMutation.isPending ? 'Saving...' : 'Save Changes'}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </AdminLayout>
  );
}
