import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Mail, UserPlus } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import AdvancedEmailEditor from './AdvancedEmailEditor';
import { useToast } from '@/hooks/use-toast';

interface EmailProvider {
  id: string;
  name: string;
  active: boolean;
  isDefault?: boolean;
}

interface EnhancedEmailEditorProps {
  subject: string;
  content: string;
  onSubjectChange: (value: string) => void;
  onContentChange: (value: string) => void;
  selectedOrder: any;
  onSendEmail: () => void;
  onCancel: () => void;
  onSaveTemplate: () => void;
  addToAllowedEmails?: boolean;
  onAddToAllowedEmailsChange?: (value: boolean) => void;
}

export default function EnhancedEmailEditor({
  subject,
  content,
  onSubjectChange,
  onContentChange,
  selectedOrder,
  onSendEmail,
  onCancel,
  onSaveTemplate,
  addToAllowedEmails = true,
  onAddToAllowedEmailsChange
}: EnhancedEmailEditorProps) {
  const [emailProviders, setEmailProviders] = useState<EmailProvider[]>([]);
  const [selectedProviderId, setSelectedProviderId] = useState<string>('');
  const [addToAllowed, setAddToAllowed] = useState<boolean>(addToAllowedEmails);
  const [m3uLink, setM3uLink] = useState<string>('');
  const { toast } = useToast();

  // Fetch email providers on component mount
  useEffect(() => {
    fetchEmailProviders();
  }, []);

  // Initialize selected provider from order data
  useEffect(() => {
    if (selectedOrder) {
      // Initialize selected provider from order
      if (selectedOrder.smtpProviderId) {
        console.log('Setting SMTP provider from order:', selectedOrder.smtpProviderId);
        setSelectedProviderId(selectedOrder.smtpProviderId);
      }
    }
  }, [selectedOrder]);

  // Update parent component when addToAllowed changes
  useEffect(() => {
    if (onAddToAllowedEmailsChange) {
      onAddToAllowedEmailsChange(addToAllowed);
    }
  }, [addToAllowed, onAddToAllowedEmailsChange]);

  const fetchEmailProviders = async () => {
    try {
      const response = await fetch('/api/admin/email-config');
      if (!response.ok) {
        throw new Error('Failed to fetch email providers');
      }

      const data = await response.json();
      if (data.providers && Array.isArray(data.providers)) {
        setEmailProviders(data.providers);

        // Set default provider if none is selected and no provider is specified in the order
        if (!selectedProviderId && data.providers.length > 0 && !selectedOrder?.smtpProviderId) {
          const defaultProvider = data.providers.find(p => p.isDefault && p.active) ||
                                 data.providers.find(p => p.active);
          if (defaultProvider) {
            setSelectedProviderId(defaultProvider.id);
          }
        }
      }
    } catch (error) {
      console.error('Error fetching email providers:', error);
      toast({
        title: 'Error',
        description: 'Failed to load email providers',
        variant: 'destructive'
      });
    }
  };

  // Extract username and password from M3U link
  const extractCredentialsFromM3U = (link: string) => {
    try {
      // Parse the URL to extract username and password
      const url = new URL(link);
      const params = new URLSearchParams(url.search);

      const username = params.get('username');
      const password = params.get('password');

      if (username && password) {
        return { username, password, success: true };
      }

      // If not found in URL params, try to extract from URL path for other formats
      const pathParts = url.pathname.split('/');
      const lastPart = pathParts[pathParts.length - 1];

      if (lastPart && lastPart.includes(':')) {
        const [extractedUsername, extractedPassword] = lastPart.split(':');
        if (extractedUsername && extractedPassword) {
          return { username: extractedUsername, password: extractedPassword, success: true };
        }
      }

      return { success: false, message: 'Could not extract username and password from the link' };
    } catch (error) {
      console.error('Error parsing M3U link:', error);
      return { success: false, message: 'Invalid URL format' };
    }
  };



  // Process M3U link and update email content
  const processM3ULink = () => {
    if (!m3uLink) {
      toast({
        title: 'No M3U Link',
        description: 'Please enter an M3U link first',
        variant: 'destructive'
      });
      return false;
    }

    const result = extractCredentialsFromM3U(m3uLink);

    if (!result.success) {
      toast({
        title: 'Extraction Failed',
        description: result.message,
        variant: 'destructive'
      });
      return false;
    }

    // Update the email content with the extracted credentials
    let updatedContent = content;

    // Always prioritize replacing the {{m3uLink}} placeholder if it exists
    if (updatedContent.includes('{{m3uLink}}')) {
      updatedContent = updatedContent.replace(/\{\{m3uLink\}\}/g, m3uLink);
    } else {
      // If no placeholder exists, show a warning and don't modify the content
      toast({
        title: 'No M3U Link Placeholder',
        description: 'The template should contain {{m3uLink}} placeholder for proper replacement. Please add it to your template.',
        variant: 'destructive'
      });
      return false;
    }

    // Replace placeholder-style username and password first
    if (updatedContent.includes('{{username}}') && result.username) {
      updatedContent = updatedContent.replace(/\{\{username\}\}/g, result.username);
    }
    if (updatedContent.includes('{{password}}') && result.password) {
      updatedContent = updatedContent.replace(/\{\{password\}\}/g, result.password);
    }

    // Replace Username placeholder if it exists (for legacy format)
    const usernameRegex = /(Username:\s*)([^\s<]+|YOUR_USERNAME)/i;
    const passwordRegex = /(Password:\s*)([^\s<]+|YOUR_PASSWORD)/i;

    // Check if both username and password fields exist
    const hasUsername = usernameRegex.test(updatedContent);
    const hasPassword = passwordRegex.test(updatedContent);

    if (hasUsername) {
      // Replace existing username
      updatedContent = updatedContent.replace(usernameRegex, `$1${result.username}`);
    }

    if (hasPassword) {
      // Replace existing password
      updatedContent = updatedContent.replace(passwordRegex, `$1${result.password}`);
    }

    // If neither username nor password exists, add them before the first "Lien:" section
    if (!hasUsername && !hasPassword) {
      const lienRegex = /(<br>Lien:)/i;
      if (lienRegex.test(updatedContent)) {
        updatedContent = updatedContent.replace(lienRegex, `<br>Username: ${result.username}<br>Password: ${result.password}$1`);
      } else {
        // If no "Lien:" section exists, add username and password after the M3U link section
        const m3uSectionEndRegex = /(———————————————<br><br>)/i;
        if (m3uSectionEndRegex.test(updatedContent)) {
          updatedContent = updatedContent.replace(m3uSectionEndRegex, `$1Username: ${result.username}<br>Password: ${result.password}<br><br>———————————————<br><br>`);
        } else {
          // As a last resort, add them at the beginning of the content
          updatedContent = `Username: ${result.username}<br>Password: ${result.password}<br><br>${updatedContent}`;
        }
      }
    } else if (hasUsername && !hasPassword) {
      // If only username exists but not password, add password after username
      updatedContent = updatedContent.replace(usernameRegex, `$1${result.username}<br>Password: ${result.password}`);
    } else if (!hasUsername && hasPassword) {
      // If only password exists but not username, add username before password
      updatedContent = updatedContent.replace(passwordRegex, `Username: ${result.username}<br>$0`);
    }

    // Update the content
    onContentChange(updatedContent);

    toast({
      title: 'Success',
      description: 'Username and password extracted and added to the email',
      variant: 'default'
    });

    return true;
  };

  // Update the order with SMTP provider before sending
  const handleSendWithUpdates = async () => {
    try {
      // Update the order with SMTP provider
      if (selectedOrder && selectedOrder.id) {
        const updateResponse = await fetch(`/api/admin/invoices/${selectedOrder.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            smtpProviderId: selectedProviderId
          })
        });

        if (!updateResponse.ok) {
          throw new Error('Failed to update order information');
        }

        console.log('Updated order with SMTP provider:', {
          smtpProviderId: selectedProviderId
        });
      }

      // Store the addToAllowed value in localStorage for the next time
      localStorage.setItem('addToAllowedEmails', addToAllowed.toString());

      // Call the original send function with the addToAllowed flag
      onSendEmail();
    } catch (error) {
      console.error('Error updating order before sending:', error);
      toast({
        title: 'Error',
        description: 'Failed to update order information',
        variant: 'destructive'
      });
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex gap-4">
        <div className="flex-1">
          <Label htmlFor="emailSubject">Subject</Label>
          <Input
            id="emailSubject"
            value={subject}
            onChange={(e) => onSubjectChange(e.target.value)}
            className="mt-1"
          />
        </div>
      </div>



      <div className="space-y-2">
        <Label>SMTP Provider</Label>
        <Select value={selectedProviderId} onValueChange={setSelectedProviderId}>
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select SMTP provider" />
          </SelectTrigger>
          <SelectContent>
            {emailProviders.map((provider) => (
              <SelectItem key={provider.id} value={provider.id} disabled={!provider.active}>
                <div className="flex items-center">
                  <Mail className="h-4 w-4 mr-2" />
                  {provider.name}
                  {provider.isDefault && <Badge className="ml-2">Default</Badge>}
                  {provider.id === selectedOrder?.smtpProviderId && (
                    <Badge variant="outline" className="ml-2 bg-blue-50">Used at checkout</Badge>
                  )}
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <p className="text-xs text-muted-foreground mt-1">
          {selectedOrder?.checkoutPageTitle
            ? `This order was created from the "${selectedOrder.checkoutPageTitle}" checkout page.`
            : 'Select the SMTP provider to use for sending this email.'}
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="m3uLink">M3U Link</Label>
        <div className="flex gap-2">
          <Input
            id="m3uLink"
            value={m3uLink}
            onChange={(e) => setM3uLink(e.target.value)}
            placeholder="http://example.com/get.php?username=user&password=pass"
            className="flex-1"
          />
          <Button
            type="button"
            onClick={processM3ULink}
            variant="secondary"
          >
            Extract & Apply
          </Button>
        </div>
        <p className="text-xs text-muted-foreground mt-1">
          Enter an M3U link to automatically extract username and password and add them to the email.
        </p>
      </div>

      <div>
        <Label htmlFor="emailContent">Email Content</Label>
        <AdvancedEmailEditor
          initialValue={content}
          onChange={onContentChange}
          height={400}
          helpText="Include links after 'Lien:' to automatically extract them."
          showPreview={true}
        />
      </div>

      <div className="flex items-center space-x-2">
        <Checkbox
          id="addToAllowedEmails"
          checked={addToAllowed}
          onCheckedChange={(checked) => setAddToAllowed(checked as boolean)}
        />
        <div className="grid gap-1.5 leading-none">
          <label
            htmlFor="addToAllowedEmails"
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center"
          >
            <UserPlus className="h-4 w-4 mr-1" />
            Add to Allowed Emails
          </label>
          <p className="text-xs text-muted-foreground">
            Automatically add this email to your allowed emails list with SMTP provider and subject information
          </p>
        </div>
      </div>

      <div className="flex justify-between space-x-2 mt-4">
        <Button
          variant="outline"
          onClick={onSaveTemplate}
          type="button"
        >
          Save as Template
        </Button>
        <div>
          <Button
            variant="outline"
            onClick={onCancel}
            className="mr-2"
          >
            Cancel
          </Button>
          <Button onClick={handleSendWithUpdates}>
            Send Email
          </Button>
        </div>
      </div>
    </div>
  );
}
