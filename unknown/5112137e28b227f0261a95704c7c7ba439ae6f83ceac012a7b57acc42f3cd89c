import { Router, Request, Response, NextFunction } from 'express';
import { storage } from '../storage';
import { insertProductSchema } from '@shared/schema';
import { ZodError } from 'zod';
import { randomBytes, createHash } from 'crypto';
import { configStorage } from '../config-storage';
import { telegramBot } from '../services/telegram-bot';

const adminRouter = Router();

// Session augmentation for TypeScript
declare module 'express-session' {
  interface SessionData {
    isAdmin: boolean;
    username: string;
    userId: number;
    rememberMe: boolean;
    requiresTwoFactor: boolean;
    twoFactorVerified: boolean;
    pendingApprovalId?: string;
    pending2FARequestId?: string;
  }
}

// Admin access token middleware
const checkAdminAccess = (req: Request, res: Response, next: NextFunction) => {
  const adminToken = process.env.ADMIN_ACCESS_TOKEN || 'admin_code';
  const providedToken = req.query.token || req.headers['x-admin-token'];

  // Accept both the new token and the old token for compatibility
  const validTokens = [adminToken, 'admin_code', 's3cur3-4dm1n-4cc3ss-t0k3n'];

  if (!validTokens.includes(providedToken as string)) {
    return res.status(404).json({ message: 'Not Found' });
  }

  next();
};

// Admin authentication middleware
const isAdmin = (req: Request, res: Response, next: NextFunction) => {
  console.log('Checking admin session:', req.session);

  // Check if user is authenticated and has completed 2FA if required
  if (req.session.isAdmin) {
    // If 2FA is required but not verified, deny access
    if (req.session.requiresTwoFactor && !req.session.twoFactorVerified) {
      console.log('2FA required but not verified');
      return res.status(401).json({
        message: 'Two-factor authentication required',
        requiresTwoFactor: true
      });
    }

    console.log('Admin session verified:', true);
    next();
  } else {
    console.log('Admin session verified:', false);
    res.status(401).json({ message: 'Unauthorized' });
  }
};

// Check if user is authenticated (no admin token required since we're just checking existing session)
adminRouter.get('/check-session', (req: Request, res: Response) => {
  console.log('Checking session:', {
    id: req.session.id,
    isAdmin: req.session.isAdmin,
    username: req.session.username,
    requiresTwoFactor: req.session.requiresTwoFactor,
    twoFactorVerified: req.session.twoFactorVerified,
    cookie: req.session.cookie
  });

  if (req.session.isAdmin) {
    // User is fully authenticated
    res.status(200).json({
      isAuthenticated: true,
      user: {
        username: req.session.username
      }
    });
  } else if (req.session.requiresTwoFactor && !req.session.twoFactorVerified) {
    // User has completed first factor but needs to complete 2FA
    res.status(200).json({
      isAuthenticated: false,
      requiresTwoFactor: true,
      message: 'Two-factor authentication required'
    });
  } else {
    // User is not authenticated at all
    res.status(200).json({
      isAuthenticated: false,
      message: 'Not authenticated'
    });
  }
});

// Admin login
adminRouter.post('/login', checkAdminAccess, async (req: Request, res: Response) => {
  const { username, password, rememberMe } = req.body;

  // Get client IP and user agent for security tracking
  const clientIP = req.ip || req.connection.remoteAddress || req.socket.remoteAddress || 'unknown';
  const userAgent = req.get('User-Agent') || 'unknown';
  const sessionId = req.session.id || 'unknown';

  console.log('Login attempt for:', username, 'from IP:', clientIP);

  try {
    // Get user from storage (single call)
    let authenticatedUser = await storage.getUserByUsername(username);

    // If user not found, create default admin user for demo purposes
    if (!authenticatedUser && username === 'admin' && password === 'admin123') {
      // Create default admin user
      authenticatedUser = await storage.createUser({
        username: 'admin',
        password: 'admin123', // This will be hashed in the storage layer
        email: '<EMAIL>',
        isAdmin: true,
        rememberMe: false
      });
    }

    // Verify credentials (optimized - use user object to avoid double lookup)
    const isValidCredentials = authenticatedUser ?
      await storage.verifyUserCredentialsWithUser(authenticatedUser, password) : false;

    if (isValidCredentials) {
      // Get security settings
      const securitySettings = await configStorage.getSecuritySettings();

      // Send login alert if enabled
      if (securitySettings.loginAlertsEnabled) {
        await telegramBot.sendLoginAlert(username, clientIP, userAgent, true, sessionId);
      }

      // Check IP restriction if enabled
      if (securitySettings.ipRestrictionEnabled) {
        const isIPApproved = await telegramBot.isIPApproved(clientIP);
        if (!isIPApproved) {
          console.log('New IP detected, requesting approval:', clientIP);

          // Request approval for new IP
          const approvalId = await telegramBot.requestLoginApproval(username, clientIP, userAgent, sessionId);

          // Store approval ID in session for checking later
          req.session.pendingApprovalId = approvalId;

          return res.status(202).json({
            message: 'New IP address detected. Approval required.',
            requiresApproval: true,
            approvalId: approvalId
          });
        }
      }

      // Check if 2FA is enabled for this user AND Telegram 2FA is enabled in settings
      if (authenticatedUser && authenticatedUser.twoFactorEnabled && securitySettings.telegram2FAEnabled) {
        // Set up session for 2FA verification
        req.session.requiresTwoFactor = true;
        req.session.userId = authenticatedUser.id;
        req.session.username = username;
        req.session.rememberMe = rememberMe || false;
        req.session.twoFactorVerified = false;
        req.session.isAdmin = false; // Will be set to true after 2FA verification

        // Set cookie expiration based on rememberMe
        req.session.cookie.maxAge = rememberMe ?
          30 * 24 * 60 * 60 * 1000 : // 30 days
          24 * 60 * 60 * 1000; // 24 hours

        // Update auto-login settings asynchronously (don't wait)
        setImmediate(async () => {
          try {
            await storage.updateAutoLoginSettings(authenticatedUser.id, rememberMe || false);
          } catch (error) {
            console.error('Error updating auto-login settings:', error);
          }
        });

        // Request 2FA approval via Telegram
        const twoFactorRequestId = await telegramBot.request2FAApproval(username, sessionId);
        req.session.pending2FARequestId = twoFactorRequestId;

        console.log('First-factor authentication successful, 2FA required:', {
          id: req.session.id,
          username: req.session.username,
          requiresTwoFactor: req.session.requiresTwoFactor,
          twoFactorRequestId: twoFactorRequestId
        });

        // Return response immediately (session auto-saves)
        return res.status(200).json({
          message: 'Two-factor authentication required',
          requiresTwoFactor: true,
          isAuthenticated: false,
          twoFactorRequestId: twoFactorRequestId
        });
      } else {
        // No 2FA required, complete login
        req.session.isAdmin = true;
        req.session.username = username;
        req.session.rememberMe = rememberMe || false;
        req.session.requiresTwoFactor = false;
        req.session.twoFactorVerified = false;

        if (authenticatedUser) {
          req.session.userId = authenticatedUser.id;
        }

        // Set cookie expiration based on rememberMe
        req.session.cookie.maxAge = rememberMe ?
          30 * 24 * 60 * 60 * 1000 : // 30 days
          24 * 60 * 60 * 1000; // 24 hours

        // Update auto-login settings asynchronously (don't wait)
        setImmediate(async () => {
          try {
            await storage.updateAutoLoginSettings(authenticatedUser.id, rememberMe || false);
          } catch (error) {
            console.error('Error updating auto-login settings:', error);
          }
        });

        console.log('Login successful, session info:', {
          id: req.session.id,
          isAdmin: req.session.isAdmin,
          username: req.session.username,
          rememberMe: req.session.rememberMe,
          cookieMaxAge: req.session.cookie.maxAge
        });

        // Set a non-httpOnly cookie as a flag for the frontend
        res.cookie('isLoggedIn', 'true', {
          httpOnly: false,
          maxAge: req.session.cookie.maxAge,
          path: '/',
          sameSite: 'lax'
        });

        // Return success response immediately (session auto-saves)
        return res.status(200).json({
          message: 'Login successful',
          user: { username },
          isAuthenticated: true,
          rememberMe: req.session.rememberMe,
          requiresTwoFactor: false
        });
      }
    } else {
      // Send login alert for failed login attempt if enabled
      const securitySettings = await configStorage.getSecuritySettings();
      if (securitySettings.loginAlertsEnabled) {
        await telegramBot.sendLoginAlert(username, clientIP, userAgent, false);
      }
      res.status(401).json({ message: 'Invalid credentials' });
    }
  } catch (error) {
    console.error('Error during login:', error);
    res.status(500).json({ message: 'Login failed - server error' });
  }
});

// Check login approval status
adminRouter.get('/check-approval/:approvalId', async (req: Request, res: Response) => {
  try {
    const { approvalId } = req.params;
    const status = telegramBot.getLoginApprovalStatus(approvalId);

    res.json({ status });
  } catch (error) {
    console.error('Error checking approval status:', error);
    res.status(500).json({ message: 'Failed to check approval status' });
  }
});

// Check 2FA status
adminRouter.get('/check-2fa/:requestId', async (req: Request, res: Response) => {
  try {
    const { requestId } = req.params;
    const status = telegramBot.get2FAStatus(requestId);

    if (status === 'approved') {
      // Complete the login process
      req.session.isAdmin = true;
      req.session.twoFactorVerified = true;
      req.session.requiresTwoFactor = false;

      // Set a non-httpOnly cookie as a flag for the frontend
      res.cookie('isLoggedIn', 'true', {
        httpOnly: false,
        maxAge: req.session.cookie.maxAge,
        path: '/',
        sameSite: 'lax'
      });
    }

    res.json({ status });
  } catch (error) {
    console.error('Error checking 2FA status:', error);
    res.status(500).json({ message: 'Failed to check 2FA status' });
  }
});

// Admin logout
adminRouter.post('/logout', (req: Request, res: Response) => {
  console.log('Logout requested, destroying session:', req.session.id);

  req.session.destroy((err) => {
    if (err) {
      console.error('Error destroying session:', err);
      return res.status(500).json({ message: 'Logout failed' });
    }

    // Clear the cookie
    res.clearCookie('isLoggedIn');
    res.clearCookie('connect.sid', { path: '/' });

    res.status(200).json({ message: 'Logout successful' });
  });
});

// Enable/Disable 2FA for current user
adminRouter.post('/toggle-2fa', isAdmin, async (req: Request, res: Response) => {
  try {
    const { enabled } = req.body;
    const userId = req.session.userId;

    if (!userId) {
      return res.status(400).json({ message: 'User ID not found in session' });
    }

    // Update user's 2FA setting
    await storage.updateUser(userId, { twoFactorEnabled: enabled });

    res.json({
      message: `Two-factor authentication ${enabled ? 'enabled' : 'disabled'} successfully`,
      twoFactorEnabled: enabled
    });
  } catch (error) {
    console.error('Error toggling 2FA:', error);
    res.status(500).json({ message: 'Failed to update 2FA settings' });
  }
});

// Get security settings
adminRouter.get('/security-settings', isAdmin, async (req: Request, res: Response) => {
  try {
    const securitySettings = await configStorage.getSecuritySettings();

    // Also get user's 2FA status
    const userId = req.session.userId;
    let userTwoFactorEnabled = false;

    if (userId) {
      const user = await storage.getUser(userId);
      if (user) {
        userTwoFactorEnabled = user.twoFactorEnabled || false;
      }
    }

    res.json({
      ...securitySettings,
      userTwoFactorEnabled,
      username: req.session.username
    });
  } catch (error) {
    console.error('Error fetching security settings:', error);
    res.status(500).json({ message: 'Failed to fetch security settings' });
  }
});

// Update security settings
adminRouter.post('/security-settings', isAdmin, async (req: Request, res: Response) => {
  try {
    const { loginAlertsEnabled, ipRestrictionEnabled, telegram2FAEnabled } = req.body;

    const updates = {
      loginAlertsEnabled: Boolean(loginAlertsEnabled),
      ipRestrictionEnabled: Boolean(ipRestrictionEnabled),
      telegram2FAEnabled: Boolean(telegram2FAEnabled)
    };

    const updatedSettings = await configStorage.updateSecuritySettings(updates);

    res.json({
      message: 'Security settings updated successfully',
      settings: updatedSettings
    });
  } catch (error) {
    console.error('Error updating security settings:', error);
    res.status(500).json({ message: 'Failed to update security settings' });
  }
});

// Get admin dashboard stats
adminRouter.get('/stats', isAdmin, async (req: Request, res: Response) => {
  try {
    const products = await storage.getProducts();
    const invoices = await storage.getInvoices();

    const activeProducts = products.filter(p => p.active).length;
    const totalOrders = invoices.length;
    const pendingInvoices = invoices.filter(i => i.status === 'pending').length;
    const paidInvoices = invoices.filter(i => i.status === 'paid').length;

    // Calculate total revenue from paid invoices
    const totalSales = invoices
      .filter(i => i.status === 'paid')
      .reduce((sum, invoice) => sum + parseFloat(invoice.amount), 0);

    // Get recent orders (last 10)
    const recentOrders = invoices
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 10)
      .map(invoice => ({
        id: invoice.id,
        customerName: invoice.customerName,
        customerEmail: invoice.customerEmail,
        amount: parseFloat(invoice.amount),
        status: invoice.status,
        createdAt: invoice.createdAt
      }));

    // Return data structure expected by frontend
    res.json({
      totalSales: totalSales,
      totalOrders: totalOrders,
      totalProducts: products.length,
      recentOrders: recentOrders,
      // Keep the old structure for backward compatibility
      products: {
        total: products.length,
        active: activeProducts
      },
      sales: {
        total: totalOrders,
        pending: pendingInvoices,
        completed: paidInvoices,
        revenue: totalSales.toFixed(2)
      }
    });
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    res.status(500).json({ message: 'Failed to fetch dashboard stats' });
  }
});

// Get all invoices
adminRouter.get('/invoices', isAdmin, async (req: Request, res: Response) => {
  try {
    const invoices = await storage.getInvoices();
    const customCheckoutPages = await storage.getCustomCheckoutPages();

    console.log('Raw invoices:', JSON.stringify(invoices, null, 2));
    console.log('Custom checkout pages:', JSON.stringify(customCheckoutPages, null, 2));

    // Enhance invoices with product name and checkout page info for display
    const enhancedInvoices = await Promise.all(invoices.map(async (invoice) => {
      const product = await storage.getProduct(invoice.productId);

      // Check if this invoice was created from a custom checkout page
      let checkoutPageInfo = null;
      if (invoice.customCheckoutPageId) {
        checkoutPageInfo = customCheckoutPages.find(page => page.id === invoice.customCheckoutPageId);
        console.log(`Found checkout page for invoice ${invoice.id}:`, checkoutPageInfo);
      } else {
        console.log(`No customCheckoutPageId for invoice ${invoice.id}`);

        // Try to find a matching checkout page based on the product ID
        // This is a fallback for older orders that don't have customCheckoutPageId
        const matchingPage = customCheckoutPages.find(page => {
          // For trial orders, find a trial checkout page
          if (invoice.isTrialOrder) {
            return page.isTrialCheckout;
          }
          // For regular orders, find a regular checkout page
          return !page.isTrialCheckout;
        });

        if (matchingPage) {
          console.log(`Found matching checkout page for invoice ${invoice.id} based on type:`, matchingPage);
          checkoutPageInfo = matchingPage;

          // Update the invoice with the checkout page ID for future reference
          await storage.updateInvoice(invoice.id, { customCheckoutPageId: matchingPage.id });
        }
      }

      return {
        ...invoice,
        productName: product ? product.name : 'Unknown Product',
        checkoutPageTitle: checkoutPageInfo ? checkoutPageInfo.title : null,
        checkoutPageId: checkoutPageInfo ? checkoutPageInfo.id : null,
        smtpProviderId: checkoutPageInfo ? checkoutPageInfo.smtpProviderId : null
      };
    }));

    res.json(enhancedInvoices);
  } catch (error) {
    console.error('Error fetching invoices:', error);
    res.status(500).json({ message: 'Failed to fetch invoices' });
  }
});

// Create new invoice (for testing purposes)
adminRouter.post('/invoices', isAdmin, async (req: Request, res: Response) => {
  try {
    const { customerName, customerEmail, productId, amount, status, createdAt } = req.body;

    // Validate required fields
    if (!customerName || !customerEmail || !productId || !amount) {
      return res.status(400).json({ message: 'Missing required fields' });
    }

    // Create the invoice
    const invoice = await storage.createInvoice({
      customerName,
      customerEmail,
      productId: Number(productId),
      amount: amount.toString(),
      status: status || 'pending',
      createdAt: createdAt || new Date().toISOString()
    });

    console.log('Created test invoice:', invoice);

    res.status(201).json(invoice);
  } catch (error) {
    console.error('Error creating invoice:', error);
    res.status(500).json({
      message: 'Failed to create invoice',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Update invoice
adminRouter.put('/invoices/:id', isAdmin, async (req: Request, res: Response) => {
  try {
    const invoiceId = parseInt(req.params.id);
    if (isNaN(invoiceId)) {
      return res.status(400).json({ message: 'Invalid invoice ID' });
    }

    const { customerName, customerEmail, status, notes, checkForTrialUpgrade } = req.body;

    // Get the existing invoice
    const existingInvoice = await storage.getInvoice(invoiceId);
    if (!existingInvoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }

    // Update the invoice
    const updatedInvoice = await storage.updateInvoice(invoiceId, {
      customerName,
      customerEmail,
      status,
      notes
    });

    console.log('Updated invoice:', updatedInvoice);

    // Check if we need to upgrade a trial order
    let trialOrderUpgraded = false;

    if (checkForTrialUpgrade && status && status.toLowerCase() === 'paid' && customerEmail) {
      console.log(`Checking for trial orders to upgrade for customer email: ${customerEmail}`);

      // Find trial orders with the same email that haven't been upgraded yet
      const allInvoices = await storage.getInvoices();
      const trialOrders = allInvoices.filter(invoice =>
        invoice.customerEmail === customerEmail &&
        invoice.isTrialOrder === true &&
        invoice.hasUpgraded !== true &&
        invoice.id !== invoiceId // Don't include the current invoice
      );

      console.log(`Found ${trialOrders.length} trial orders for customer ${customerEmail} that can be upgraded`);

      if (trialOrders.length > 0) {
        // Upgrade the trial order(s)
        for (const trialOrder of trialOrders) {
          await storage.updateInvoice(trialOrder.id, {
            hasUpgraded: true,
            upgradedAt: new Date().toISOString()
          });
          console.log(`Automatically upgraded trial order ${trialOrder.id} for customer ${customerEmail}`);
          trialOrderUpgraded = true;
        }
      }
    }

    res.json({
      message: 'Invoice updated successfully',
      invoice: updatedInvoice,
      trialOrderUpgraded
    });
  } catch (error) {
    console.error('Error updating invoice:', error);
    res.status(500).json({
      message: 'Failed to update invoice',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Mark invoice as upgraded
adminRouter.put('/invoices/:id/upgrade', isAdmin, async (req: Request, res: Response) => {
  try {
    const invoiceId = parseInt(req.params.id);
    if (isNaN(invoiceId)) {
      return res.status(400).json({ message: 'Invalid invoice ID' });
    }

    const { hasUpgraded, upgradedAt } = req.body;

    // Get the existing invoice
    const existingInvoice = await storage.getInvoice(invoiceId);
    if (!existingInvoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }

    // Update the invoice
    const updatedInvoice = await storage.updateInvoice(invoiceId, {
      hasUpgraded,
      upgradedAt
    });

    console.log('Marked invoice as upgraded:', updatedInvoice);

    res.json({
      message: 'Invoice marked as upgraded successfully',
      invoice: updatedInvoice
    });
  } catch (error) {
    console.error('Error marking invoice as upgraded:', error);
    res.status(500).json({
      message: 'Failed to mark invoice as upgraded',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Delete invoice (remove customer)
adminRouter.delete('/invoices/:id', isAdmin, async (req: Request, res: Response) => {
  try {
    const invoiceId = parseInt(req.params.id);
    if (isNaN(invoiceId)) {
      return res.status(400).json({ message: 'Invalid invoice ID' });
    }

    // Get the existing invoice
    const existingInvoice = await storage.getInvoice(invoiceId);
    if (!existingInvoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }

    // Delete the invoice from storage
    const success = await storage.deleteInvoice(invoiceId);
    if (!success) {
      throw new Error('Failed to delete invoice from database');
    }
    console.log(`Deleted invoice with ID ${invoiceId}`);

    res.json({
      message: 'Customer removed successfully',
      invoiceId
    });
  } catch (error) {
    console.error('Error removing customer:', error);
    res.status(500).json({
      message: 'Failed to remove customer',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Bulk delete invoices (remove customers)
adminRouter.post('/invoices/bulk-delete', isAdmin, async (req: Request, res: Response) => {
  try {
    const { ids } = req.body;

    if (!Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({ message: 'Invalid or empty invoice IDs array' });
    }

    // Validate all IDs are numbers
    const invoiceIds = ids.map(id => parseInt(id)).filter(id => !isNaN(id));
    if (invoiceIds.length !== ids.length) {
      return res.status(400).json({ message: 'All IDs must be valid numbers' });
    }

    // Bulk delete invoices
    const result = await storage.bulkDeleteInvoices(invoiceIds);
    console.log(`Bulk deleted invoices: ${result.success} successful, ${result.failed} failed`);

    res.status(200).json({
      message: `Successfully removed ${result.success} customers. ${result.failed} failed.`,
      success: result.success,
      failed: result.failed
    });
  } catch (error) {
    console.error('Error bulk deleting invoices:', error);
    res.status(500).json({ message: 'Failed to bulk remove customers' });
  }
});

// Get all products for admin
adminRouter.get('/products', isAdmin, async (req: Request, res: Response) => {
  try {
    const products = await storage.getProducts();
    res.json(products);
  } catch (error) {
    console.error('Error fetching products:', error);
    res.status(500).json({ message: 'Failed to fetch products' });
  }
});

// Create new product
adminRouter.post('/products', isAdmin, async (req: Request, res: Response) => {
  try {
    const productData = insertProductSchema.parse(req.body);
    const product = await storage.createProduct(productData);
    res.status(201).json(product);
  } catch (error) {
    console.error('Error creating product:', error);

    if (error instanceof ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({ message: 'Failed to create product' });
  }
});

// Update product
adminRouter.patch('/products/:id', isAdmin, async (req: Request, res: Response) => {
  try {
    const productId = parseInt(req.params.id);
    if (isNaN(productId)) {
      return res.status(400).json({ message: 'Invalid product ID' });
    }

    // In a real application with a database, this would use a real update operation
    // For our in-memory store, we'll get the product, validate the update, and simulate the update
    const existingProduct = await storage.getProduct(productId);
    if (!existingProduct) {
      return res.status(404).json({ message: 'Product not found' });
    }

    // Since we don't have a real update method in storage, let's simulate it
    // In a real app with a DB, you'd use storage.updateProduct() instead
    const updatedProduct = {
      ...existingProduct,
      ...req.body,
      id: productId  // Ensure ID doesn't change
    };

    // Validate the combined object
    insertProductSchema.parse(updatedProduct);

    // Simulate updating the product (would be a DB update in a real app)
    // This is a hack for our demo app; in a real app with a DB, you'd use storage.updateProduct
    const allProducts = await storage.getProducts();
    const productIndex = allProducts.findIndex(p => p.id === productId);
    if (productIndex !== -1) {
      allProducts[productIndex] = updatedProduct;
    }

    res.json(updatedProduct);
  } catch (error) {
    console.error('Error updating product:', error);

    if (error instanceof ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({ message: 'Failed to update product' });
  }
});

// Delete product
adminRouter.delete('/products/:id', isAdmin, async (req: Request, res: Response) => {
  try {
    const productId = parseInt(req.params.id);
    if (isNaN(productId)) {
      return res.status(400).json({ message: 'Invalid product ID' });
    }

    // Check if product exists
    const existingProduct = await storage.getProduct(productId);
    if (!existingProduct) {
      return res.status(404).json({ message: 'Product not found' });
    }

    // Delete the product from storage
    const success = await storage.deleteProduct(productId);
    if (!success) {
      throw new Error('Failed to delete product from database');
    }
    console.log(`Deleted product with ID ${productId}`);

    res.status(200).json({ message: 'Product deleted successfully' });
  } catch (error) {
    console.error('Error deleting product:', error);
    res.status(500).json({ message: 'Failed to delete product' });
  }
});

// Bulk delete products
adminRouter.post('/products/bulk-delete', isAdmin, async (req: Request, res: Response) => {
  try {
    const { ids } = req.body;

    if (!Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({ message: 'Invalid or empty product IDs array' });
    }

    // Validate all IDs are numbers
    const productIds = ids.map(id => parseInt(id)).filter(id => !isNaN(id));
    if (productIds.length !== ids.length) {
      return res.status(400).json({ message: 'All IDs must be valid numbers' });
    }

    // Bulk delete products
    const result = await storage.bulkDeleteProducts(productIds);
    console.log(`Bulk deleted products: ${result.success} successful, ${result.failed} failed`);

    res.status(200).json({
      message: `Successfully deleted ${result.success} products. ${result.failed} failed.`,
      success: result.success,
      failed: result.failed
    });
  } catch (error) {
    console.error('Error bulk deleting products:', error);
    res.status(500).json({ message: 'Failed to bulk delete products' });
  }
});

// Email configuration endpoints
adminRouter.get('/email-config', isAdmin, async (req: Request, res: Response) => {
  try {
    const emailConfig = await storage.getEmailConfig();
    res.json(emailConfig);
  } catch (error) {
    console.error('Error fetching email config:', error);
    res.status(500).json({ message: 'Failed to fetch email configuration' });
  }
});

adminRouter.post('/email-config', isAdmin, async (req: Request, res: Response) => {
  try {
    const { providerId, name, config, active, isDefault, isBackup } = req.body;

    const providerData = {
      id: providerId,
      name: name || 'SMTP Configuration',
      host: config.host || '',
      port: config.port || '587',
      secure: config.secure || false,
      username: config.username || '',
      password: config.password || '',
      fromEmail: config.fromEmail || '',
      fromName: config.fromName || '',
      active: active || false,
      isDefault: isDefault || false,
      isBackup: isBackup || false
    };

    await storage.saveSmtpProvider(providerData);

    const updatedConfig = await storage.getEmailConfig();
    res.json({
      message: 'Email configuration updated successfully',
      config: updatedConfig
    });
  } catch (error) {
    console.error('Error updating email config:', error);
    res.status(500).json({ message: 'Failed to update email configuration' });
  }
});

adminRouter.post('/email-config/delete', isAdmin, async (req: Request, res: Response) => {
  try {
    const { providerId } = req.body;

    // Get current config to check if this is the default provider
    const emailConfig = await storage.getEmailConfig();
    const provider = emailConfig.providers.find((p: any) => p.id === providerId);

    if (!provider) {
      return res.status(404).json({ message: 'Email configuration not found' });
    }

    if (provider.isDefault) {
      return res.status(400).json({
        message: 'Cannot delete the default SMTP configuration. Please set another configuration as default first.'
      });
    }

    await storage.deleteSmtpProvider(providerId);

    const updatedConfig = await storage.getEmailConfig();
    res.json({
      message: 'Email configuration deleted successfully',
      config: updatedConfig
    });
  } catch (error) {
    console.error('Error deleting email config:', error);
    res.status(500).json({ message: 'Failed to delete email configuration' });
  }
});

// Export SMTP providers
adminRouter.get('/email-config/export', isAdmin, async (req: Request, res: Response) => {
  try {
    const exportData = await storage.exportSmtpProviders();

    // Set headers for file download
    const filename = `smtp-providers-${new Date().toISOString().split('T')[0]}.json`;
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

    res.json(exportData);
  } catch (error) {
    console.error('Error exporting SMTP providers:', error);
    res.status(500).json({ message: 'Failed to export SMTP providers' });
  }
});

// Import SMTP providers
adminRouter.post('/email-config/import', isAdmin, async (req: Request, res: Response) => {
  try {
    const { importData, options = {} } = req.body;

    if (!importData) {
      return res.status(400).json({ message: 'Import data is required' });
    }

    const results = await storage.importSmtpProviders(importData, options);

    const updatedConfig = await storage.getEmailConfig();
    res.json({
      message: 'SMTP providers import completed',
      results,
      config: updatedConfig
    });
  } catch (error) {
    console.error('Error importing SMTP providers:', error);
    res.status(500).json({
      message: 'Failed to import SMTP providers',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Create missing SMTP provider for checkout pages
adminRouter.post('/create-missing-smtp-provider', isAdmin, async (req: Request, res: Response) => {
  try {
    console.log('🔧 Creating missing SMTP provider for checkout pages...');

    // Get current email config to see what exists
    const emailConfig = await storage.getEmailConfig();
    console.log(`📤 Current SMTP providers: ${emailConfig.providers.map(p => `${p.id} (${p.name})`).join(', ')}`);

    // Check if smtp-1748708212541 exists
    const targetProviderId = 'smtp-1748708212541';
    const existingProvider = emailConfig.providers.find(p => p.id === targetProviderId);

    if (existingProvider) {
      return res.json({
        message: 'SMTP provider already exists',
        providerId: targetProviderId,
        provider: existingProvider
      });
    }

    // Create the missing SMTP provider using the same credentials as the default
    const defaultProvider = emailConfig.providers.find(p => p.isDefault) || emailConfig.providers[0];

    if (!defaultProvider) {
      return res.status(400).json({ message: 'No default SMTP provider found to copy from' });
    }

    const newProvider = {
      id: targetProviderId,
      name: 'Checkout Page SMTP',
      host: defaultProvider.credentials.host,
      port: defaultProvider.credentials.port.toString(),
      secure: defaultProvider.credentials.secure,
      username: defaultProvider.credentials.auth.user,
      password: defaultProvider.credentials.auth.pass,
      fromEmail: defaultProvider.credentials.fromEmail,
      fromName: defaultProvider.credentials.fromName,
      active: true,
      isDefault: false,
      isBackup: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    console.log(`➕ Creating SMTP provider: ${newProvider.id} (${newProvider.name})`);

    // Insert the new provider into the database
    await storage.createSmtpProvider(newProvider);

    console.log('✅ SMTP provider created successfully');

    // Get updated config
    const updatedConfig = await storage.getEmailConfig();

    res.json({
      message: 'Missing SMTP provider created successfully',
      providerId: targetProviderId,
      provider: newProvider,
      totalProviders: updatedConfig.providers.length
    });
  } catch (error) {
    console.error('Error creating missing SMTP provider:', error);
    res.status(500).json({
      message: 'Failed to create missing SMTP provider',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Fix SMTP provider IDs in checkout pages
adminRouter.post('/fix-smtp-providers', isAdmin, async (req: Request, res: Response) => {
  try {
    console.log('🔧 Starting SMTP provider ID fix...');

    // Get all SMTP providers
    const emailConfig = await storage.getEmailConfig();
    console.log(`📤 Found ${emailConfig.providers.length} SMTP providers`);

    // Get all checkout pages
    const checkoutPages = await storage.getCustomCheckoutPages();
    console.log(`📋 Found ${checkoutPages.length} checkout pages`);

    const results = [];

    for (const page of checkoutPages) {
      const result = {
        pageId: page.id,
        pageTitle: page.title,
        oldSmtpProviderId: page.smtpProviderId,
        newSmtpProviderId: null,
        status: 'no_change'
      };

      if (page.smtpProviderId) {
        // Check if the current SMTP provider ID exists
        const providerExists = emailConfig.providers.find(p => p.id === page.smtpProviderId);

        if (!providerExists) {
          // Find the first active SMTP provider as replacement
          const activeProvider = emailConfig.providers.find(p => p.active);
          if (activeProvider) {
            console.log(`🔄 Updating checkout page "${page.title}" SMTP provider from ${page.smtpProviderId} to ${activeProvider.id}`);

            // Update the checkout page
            await storage.updateCustomCheckoutPage(page.id, {
              ...page,
              smtpProviderId: activeProvider.id
            });

            result.newSmtpProviderId = activeProvider.id;
            result.status = 'updated';
          } else {
            result.status = 'no_active_provider';
          }
        } else {
          result.status = 'provider_exists';
        }
      } else {
        // No SMTP provider set, assign the first active one
        const activeProvider = emailConfig.providers.find(p => p.active);
        if (activeProvider) {
          console.log(`➕ Setting SMTP provider for checkout page "${page.title}" to ${activeProvider.id}`);

          await storage.updateCustomCheckoutPage(page.id, {
            ...page,
            smtpProviderId: activeProvider.id
          });

          result.newSmtpProviderId = activeProvider.id;
          result.status = 'assigned';
        } else {
          result.status = 'no_active_provider';
        }
      }

      results.push(result);
    }

    console.log('✅ SMTP provider ID fix completed');

    res.json({
      message: 'SMTP provider IDs fixed successfully',
      results,
      summary: {
        total: results.length,
        updated: results.filter(r => r.status === 'updated').length,
        assigned: results.filter(r => r.status === 'assigned').length,
        noChange: results.filter(r => r.status === 'no_change' || r.status === 'provider_exists').length,
        errors: results.filter(r => r.status === 'no_active_provider').length
      }
    });
  } catch (error) {
    console.error('Error fixing SMTP provider IDs:', error);
    res.status(500).json({
      message: 'Failed to fix SMTP provider IDs',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

adminRouter.post('/email-test', isAdmin, async (req: Request, res: Response) => {
  const { email, providerId } = req.body;

  if (!email) {
    return res.status(400).json({ message: 'Email address is required' });
  }

  try {
    // Import dynamically to avoid circular dependencies
    const { sendTestEmail } = await import('../services/email');

    // Send actual test email
    const success = await sendTestEmail(email, providerId);

    if (success) {
      res.json({
        message: `Test email sent to ${email}`,
        success: true
      });
    } else {
      res.status(500).json({
        message: 'Failed to send test email. Please check your SMTP configuration.',
        success: false
      });
    }
  } catch (error) {
    console.error('Error sending test email:', error);
    res.status(500).json({
      message: 'Failed to send test email due to an error',
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Payment configuration endpoints
// Ensure trial payment providers exist
function ensureTrialPaymentProvidersExist() {
  console.log('Checking if trial payment providers exist...');
  console.log('Current payment providers:', configStorage.payment.providers.map(p => p.id));
  console.log('Current payment config:', JSON.stringify(configStorage.payment, null, 2));

  // Check if trial-custom-link provider exists
  const trialCustomLinkProviderIndex = configStorage.payment.providers.findIndex(p => p.id === 'trial-custom-link');
  if (trialCustomLinkProviderIndex === -1) {
    console.log('Creating trial-custom-link provider...');
    configStorage.payment.providers.push({
      id: 'trial-custom-link',
      name: 'Trial Custom Payment Links',
      active: true,
      config: {
        links: [
          {
            id: 'trial-link-1',
            name: 'Default Trial Payment Link',
            paymentLink: 'https://example.com/pay-trial',
            buttonText: 'Start Trial',
            successRedirectUrl: 'https://example.com/thank-you-trial',
            active: true
          },
          {
            id: 'trial-link-2',
            name: 'PayPal.me Trial Link',
            paymentLink: 'https://paypal.me/enzidswan/10',
            buttonText: 'Start Trial with PayPal',
            successRedirectUrl: 'https://example.com/trial-started',
            active: true
          },
          {
            id: 'trial-link-3',
            name: 'Stripe Trial Link',
            paymentLink: 'https://buy.stripe.com/test_trial',
            buttonText: 'Start Trial with Stripe',
            successRedirectUrl: '',
            active: true
          }
        ],
        rotationMethod: 'round-robin',
        lastUsedIndex: 0
      }
    });
  }

  // Check if trial-paypal-button-embed provider exists
  const trialPaypalButtonEmbedProviderIndex = configStorage.payment.providers.findIndex(p => p.id === 'trial-paypal-button-embed');
  if (trialPaypalButtonEmbedProviderIndex === -1) {
    console.log('Creating trial-paypal-button-embed provider...');
    configStorage.payment.providers.push({
      id: 'trial-paypal-button-embed',
      name: 'Trial PayPal Button Embed',
      active: true,
      config: {
        buttons: [
          {
            id: 'trial-button-1',
            name: 'Default Trial PayPal Button',
            buttonHtml: `
<div style="text-align: center; margin: 20px 0;">
  <div style="max-width: 500px; margin: 0 auto; border: 1px solid #e0e0e0; border-radius: 5px; padding: 15px; background-color: #f5f8ff;">
    <div style="font-size: 18px; font-weight: bold; margin-bottom: 15px; color: #2c2e2f;">
      Start Trial for {PRODUCT_NAME}
    </div>
    <div style="font-size: 24px; font-weight: bold; margin-bottom: 20px; color: #0070ba;">
      \${'{AMOUNT}'}
    </div>
    <div style="margin: 20px 0;">
      <a href="https://www.paypal.com/cgi-bin/webscr?cmd=_xclick&business=<EMAIL>&item_name=Trial: {PRODUCT_NAME}&amount={AMOUNT}&currency_code=USD&custom={PAYMENT_ID}"
         style="display: inline-block; padding: 12px 24px; background-color: #0070ba; color: white; text-decoration: none; border-radius: 4px; font-weight: bold; font-size: 16px;">
        Start Trial with PayPal
      </a>
    </div>
    <div style="margin-top: 15px; font-size: 12px; color: #666;">
      Payment ID: {PAYMENT_ID}
    </div>
    <div style="margin-top: 10px;">
      <img src="https://www.paypalobjects.com/webstatic/en_US/i/buttons/pp-acceptance-medium.png" alt="PayPal Acceptance Mark">
    </div>
    <div style="margin-top: 10px; padding: 8px; background-color: #e6f7ff; border-radius: 4px; font-size: 12px; color: #0070ba;">
      This is a trial subscription. You can upgrade to a full subscription later.
    </div>
  </div>
</div>`,
            description: 'Default trial PayPal button for testing',
            active: true
          },
          {
            id: 'trial-button-2',
            name: 'Modern Trial PayPal Button',
            buttonHtml: `
<div style="text-align: center; margin: 20px 0;">
  <div style="max-width: 500px; margin: 0 auto; border: 1px solid #e0e0e0; border-radius: 10px; padding: 20px; background-color: #f8faff; box-shadow: 0 4px 6px rgba(0,0,0,0.05);">
    <div style="font-size: 20px; font-weight: bold; margin-bottom: 15px; color: #2c2e2f;">
      Start Your Trial Today
    </div>
    <div style="font-size: 16px; margin-bottom: 15px; color: #4a5568;">
      {PRODUCT_NAME} - Limited Time Offer
    </div>
    <div style="font-size: 28px; font-weight: bold; margin-bottom: 20px; color: #0070ba;">
      \${'{AMOUNT}'}
    </div>
    <div style="margin: 20px 0;">
      <a href="https://www.paypal.com/cgi-bin/webscr?cmd=_xclick&business=<EMAIL>&item_name=Trial: {PRODUCT_NAME}&amount={AMOUNT}&currency_code=USD&custom={PAYMENT_ID}"
         style="display: inline-block; padding: 14px 28px; background-color: #0070ba; color: white; text-decoration: none; border-radius: 50px; font-weight: bold; font-size: 16px; transition: all 0.3s ease;">
        Begin Your Trial
      </a>
    </div>
    <div style="margin-top: 15px; font-size: 12px; color: #666;">
      Secure Payment via PayPal - Order ID: {PAYMENT_ID}
    </div>
    <div style="margin-top: 10px;">
      <img src="https://www.paypalobjects.com/webstatic/en_US/i/buttons/pp-acceptance-medium.png" alt="PayPal Acceptance Mark">
    </div>
    <div style="margin-top: 15px; padding: 10px; background-color: #e6f7ff; border-radius: 8px; font-size: 13px; color: #0070ba;">
      <strong>Trial Benefits:</strong> Full access to all features for a limited time. Upgrade anytime.
    </div>
  </div>
</div>`,
            description: 'Modern styled trial PayPal button with rounded corners',
            active: true
          },
          {
            id: 'trial-button-3',
            name: 'Minimalist Trial Button',
            buttonHtml: `
<div style="text-align: center; margin: 20px 0;">
  <div style="max-width: 450px; margin: 0 auto; border: 1px solid #eaeaea; border-radius: 4px; padding: 20px; background-color: #ffffff;">
    <div style="font-size: 18px; font-weight: 500; margin-bottom: 10px; color: #333;">
      Try {PRODUCT_NAME}
    </div>
    <div style="font-size: 22px; font-weight: 600; margin-bottom: 20px; color: #0070ba;">
      \${'{AMOUNT}'} Trial
    </div>
    <div style="margin: 20px 0;">
      <a href="https://www.paypal.com/cgi-bin/webscr?cmd=_xclick&business=<EMAIL>&item_name=Trial: {PRODUCT_NAME}&amount={AMOUNT}&currency_code=USD&custom={PAYMENT_ID}"
         style="display: inline-block; padding: 12px 24px; background-color: #333; color: white; text-decoration: none; border-radius: 3px; font-weight: 500; font-size: 15px;">
        Start Trial
      </a>
    </div>
    <div style="margin-top: 15px; font-size: 12px; color: #777;">
      Secure payment processing by PayPal
    </div>
    <div style="margin-top: 10px;">
      <img src="https://www.paypalobjects.com/webstatic/en_US/i/buttons/pp-acceptance-small.png" alt="PayPal">
    </div>
  </div>
</div>`,
            description: 'Minimalist trial button with clean design',
            active: true
          }
        ],
        rotationMethod: 'round-robin',
        lastUsedIndex: 0
      }
    });
  }
}

adminRouter.get('/payment-config', isAdmin, (req: Request, res: Response) => {
  // Ensure trial payment providers exist
  ensureTrialPaymentProvidersExist();

  res.json(configStorage.payment);
});

adminRouter.post('/payment-config/paypal', isAdmin, (req: Request, res: Response) => {
  const { clientId, clientSecret, mode, webhookId, active, paypalEmail } = req.body;

  // Find the PayPal provider to update
  const providerIndex = configStorage.payment.providers.findIndex(p => p.id === 'paypal');
  if (providerIndex !== -1) {
    // If this provider is being activated, deactivate all other providers
    if (active) {
      configStorage.payment.providers.forEach((provider, index) => {
        if (provider.id !== 'paypal') {
          configStorage.payment.providers[index].active = false;
        }
      });
    }

    // Update the provider config
    configStorage.payment.providers[providerIndex] = {
      ...configStorage.payment.providers[providerIndex],
      active,
      config: {
        clientId: clientId || '',
        clientSecret: clientSecret || '',
        mode: mode || 'sandbox',
        webhookId: webhookId || '',
        paypalEmail: paypalEmail || '<EMAIL>' // Email associated with your PayPal account
      }
    };
  }

  console.log('Updated PayPal config:',
    JSON.stringify(configStorage.payment.providers[providerIndex], null, 2));

  res.json({
    message: active
      ? 'PayPal configuration updated and activated. Custom Payment Link has been deactivated.'
      : 'PayPal configuration updated successfully',
    config: configStorage.payment
  });
});

adminRouter.post('/payment-config/custom-link', isAdmin, (req: Request, res: Response) => {
  const { active, rotationMethod } = req.body;

  // Find the Custom Link provider to update
  const providerIndex = configStorage.payment.providers.findIndex(p => p.id === 'custom-link');
  if (providerIndex !== -1) {
    // If this provider is being activated, deactivate all other providers
    if (active) {
      configStorage.payment.providers.forEach((provider, index) => {
        if (provider.id !== 'custom-link') {
          configStorage.payment.providers[index].active = false;
        }
      });
    }

    // Get the current config to preserve links
    const currentConfig = configStorage.payment.providers[providerIndex].config || {};
    const links = Array.isArray((currentConfig as any).links) ? (currentConfig as any).links : [];

    // Update the provider config
    configStorage.payment.providers[providerIndex] = {
      ...configStorage.payment.providers[providerIndex],
      active,
      config: {
        links,
        rotationMethod: rotationMethod || 'round-robin',
        lastUsedIndex: (currentConfig as any).lastUsedIndex || 0
      }
    };
  }

  console.log('Updated Custom Link config:',
    JSON.stringify(configStorage.payment.providers[providerIndex], null, 2));

  res.json({
    message: active
      ? 'Custom Payment Links configuration updated and activated. PayPal has been deactivated.'
      : 'Custom Payment Links configuration updated successfully',
    config: configStorage.payment
  });
});

// Update Trial Custom Link configuration
adminRouter.post('/payment-config/trial-custom-link', isAdmin, (req: Request, res: Response) => {
  const { active, rotationMethod } = req.body;

  // Find the Trial Custom Link provider to update
  const providerIndex = configStorage.payment.providers.findIndex(p => p.id === 'trial-custom-link');
  if (providerIndex !== -1) {
    // Get the current config to preserve links
    const currentConfig = configStorage.payment.providers[providerIndex].config || {};
    const links = Array.isArray((currentConfig as any).links) ? (currentConfig as any).links : [];

    // Update the provider config
    configStorage.payment.providers[providerIndex] = {
      ...configStorage.payment.providers[providerIndex],
      active,
      config: {
        links,
        rotationMethod: rotationMethod || 'round-robin',
        lastUsedIndex: (currentConfig as any).lastUsedIndex || 0
      }
    };
  }

  console.log('Updated Trial Custom Link config:',
    JSON.stringify(configStorage.payment.providers[providerIndex], null, 2));

  res.json({
    message: 'Trial Custom Payment Links configuration updated successfully',
    config: configStorage.payment
  });
});

// Create a test sale for testing purposes
adminRouter.post('/create-test-sale', isAdmin, async (req: Request, res: Response) => {
  try {
    const { customerName, customerEmail, productId, amount } = req.body;

    // Create a test invoice
    const invoiceId = `TEST-${Date.now()}`;
    const invoice = await storage.createInvoice({
      customerName: customerName || 'Test Customer',
      customerEmail: customerEmail || '<EMAIL>',
      productId: productId || 3, // Default to Productivity App Template
      amount: amount || 79.99,
      status: 'draft',
      paypalInvoiceId: invoiceId,
      paypalInvoiceUrl: 'https://www.sandbox.paypal.com/invoice/manage',
      createdAt: new Date().toISOString(),
      notes: 'Test invoice created for demonstration purposes'
    });

    console.log('Created test sale:', invoice);

    res.status(201).json({
      message: 'Test sale created successfully',
      invoiceId,
      invoice
    });
  } catch (error) {
    console.error('Error creating test sale:', error);
    res.status(500).json({
      message: 'Failed to create test sale',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Force clear all data - for clean setup
adminRouter.post('/force-clear-all-data', isAdmin, async (req: Request, res: Response) => {
  try {
    console.log('🧹 Force clearing all data for clean setup...');

    // Clear all data from storage
    const results = {
      invoices: 0,
      checkoutPages: 0,
      allowedEmails: 0,
      emailTemplates: 0,
      paypalButtons: 0,
      contactInquiries: 0
    };

    // Clear invoices
    const invoices = await storage.getInvoices();
    for (const invoice of invoices) {
      if (invoice.id) {
        await storage.deleteInvoice(invoice.id);
        results.invoices++;
      }
    }

    // Clear checkout pages
    const checkoutPages = await storage.getCustomCheckoutPages();
    for (const page of checkoutPages) {
      if (page.id) {
        await storage.deleteCustomCheckoutPage(page.id);
        results.checkoutPages++;
      }
    }

    // Clear allowed emails
    const allowedEmails = await storage.getAllowedEmails();
    for (const email of allowedEmails) {
      if (email.id) {
        await storage.deleteAllowedEmail(email.id);
        results.allowedEmails++;
      }
    }

    // Clear email templates
    const emailTemplates = await storage.getEmailTemplates();
    for (const template of emailTemplates) {
      if (template.id) {
        await storage.deleteEmailTemplate(template.id);
        results.emailTemplates++;
      }
    }

    // Clear PayPal buttons
    const paypalButtons = await storage.getPaypalButtons();
    for (const button of paypalButtons) {
      if (button.id) {
        await storage.deletePaypalButton(button.id);
        results.paypalButtons++;
      }
    }

    console.log('🧹 Data clearing completed:', results);

    res.json({
      success: true,
      message: 'All data cleared successfully - ready for your setup!',
      results
    });
  } catch (error) {
    console.error('Error clearing data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to clear data'
    });
  }
});

// Direct endpoint for adding trial payment links (called by frontend)
adminRouter.post('/direct-trial-link', isAdmin, (req: Request, res: Response) => {
  console.log('Direct trial link endpoint called with body:', req.body);

  const { name, paymentLink, buttonText, successRedirectUrl, active } = req.body;

  // Ensure trial payment providers exist
  ensureTrialPaymentProvidersExist();

  // Find the Trial Custom Link provider
  const providerIndex = configStorage.payment.providers.findIndex(p => p.id === 'trial-custom-link');
  console.log('Trial custom link provider index:', providerIndex);

  if (providerIndex === -1) {
    console.error('Trial custom payment link provider not found');
    return res.status(404).json({
      message: 'Trial custom payment link provider not found'
    });
  }

  // Get the current links
  const config = configStorage.payment.providers[providerIndex].config || {};
  const links = Array.isArray((config as any).links) ? (config as any).links : [];

  // Generate a unique ID for the new link
  const linkId = `trial-link-${Date.now()}`;

  // Add the new link
  const newLink = {
    id: linkId,
    name: name || `Trial Payment Link ${links.length + 1}`,
    paymentLink: paymentLink || '',
    buttonText: buttonText || 'Start Trial',
    successRedirectUrl: successRedirectUrl || '',
    active: active !== undefined ? active : true
  };

  links.push(newLink);

  // Update the config
  configStorage.payment.providers[providerIndex].config = {
    ...(config as any),
    links
  };

  console.log('Added new trial custom payment link (direct endpoint):', JSON.stringify(newLink, null, 2));

  res.json({
    success: true,
    message: 'Trial payment link added successfully',
    link: newLink
  });
});

// Update PayPal Button Embed configuration
adminRouter.post('/payment-config/paypal-button-embed', isAdmin, (req: Request, res: Response) => {
  const { active, rotationMethod } = req.body;

  // Find the PayPal Button Embed provider to update
  const providerIndex = configStorage.payment.providers.findIndex(p => p.id === 'paypal-button-embed');
  if (providerIndex !== -1) {
    // If this provider is being activated, deactivate all other providers
    if (active) {
      configStorage.payment.providers.forEach((provider, index) => {
        if (provider.id !== 'paypal-button-embed') {
          configStorage.payment.providers[index].active = false;
        }
      });
    }

    // Get the current config to preserve buttons
    const currentConfig = configStorage.payment.providers[providerIndex].config || {};
    const buttons = Array.isArray((currentConfig as any).buttons) ? (currentConfig as any).buttons : [];

    // Update the provider config
    configStorage.payment.providers[providerIndex] = {
      ...configStorage.payment.providers[providerIndex],
      active,
      config: {
        buttons,
        rotationMethod: rotationMethod || 'round-robin',
        lastUsedIndex: (currentConfig as any).lastUsedIndex || 0
      }
    };
  }

  console.log('Updated PayPal Button Embed config:',
    JSON.stringify(configStorage.payment.providers[providerIndex], null, 2));

  res.json({
    message: active
      ? 'PayPal Button Embed configuration updated and activated. Other payment methods have been deactivated.'
      : 'PayPal Button Embed configuration updated successfully',
    config: configStorage.payment
  });
});

// Update Trial PayPal Button Embed configuration
adminRouter.post('/payment-config/trial-paypal-button-embed', isAdmin, (req: Request, res: Response) => {
  const { active, rotationMethod } = req.body;

  // Find the Trial PayPal Button Embed provider to update
  const providerIndex = configStorage.payment.providers.findIndex(p => p.id === 'trial-paypal-button-embed');
  if (providerIndex !== -1) {
    // Get the current config to preserve buttons
    const currentConfig = configStorage.payment.providers[providerIndex].config || {};
    const buttons = Array.isArray((currentConfig as any).buttons) ? (currentConfig as any).buttons : [];

    // Update the provider config
    configStorage.payment.providers[providerIndex] = {
      ...configStorage.payment.providers[providerIndex],
      active,
      config: {
        buttons,
        rotationMethod: rotationMethod || 'round-robin',
        lastUsedIndex: (currentConfig as any).lastUsedIndex || 0
      }
    };
  }

  console.log('Updated Trial PayPal Button Embed config:',
    JSON.stringify(configStorage.payment.providers[providerIndex], null, 2));

  res.json({
    message: 'Trial PayPal Button Embed configuration updated successfully',
    config: configStorage.payment
  });
});

// Add a new PayPal button embed
adminRouter.post('/payment-config/paypal-button-embed/add', isAdmin, (req: Request, res: Response) => {
  const { name, buttonHtml, description, active } = req.body;

  // Find the PayPal Button Embed provider
  const providerIndex = configStorage.payment.providers.findIndex(p => p.id === 'paypal-button-embed');
  if (providerIndex === -1) {
    return res.status(404).json({
      message: 'PayPal Button Embed provider not found'
    });
  }

  // Get the current buttons
  const config = configStorage.payment.providers[providerIndex].config || {};
  const buttons = Array.isArray((config as any).buttons) ? (config as any).buttons : [];

  // Generate a unique ID for the new button
  const buttonId = `button-${Date.now()}`;

  // Add the new button
  buttons.push({
    id: buttonId,
    name: name || `PayPal Button ${buttons.length + 1}`,
    buttonHtml: buttonHtml || '',
    description: description || '',
    active: active !== undefined ? active : true
  });

  // Update the config
  configStorage.payment.providers[providerIndex].config = {
    ...(config as any),
    buttons
  };

  console.log('Added new PayPal button embed:',
    JSON.stringify(buttons[buttons.length - 1], null, 2));

  res.json({
    message: 'PayPal button embed added successfully',
    buttonId,
    config: configStorage.payment
  });
});

// Add a new Trial PayPal button embed
adminRouter.post('/payment-config/trial-paypal-button-embed/add', isAdmin, (req: Request, res: Response) => {
  const { name, buttonHtml, description, active } = req.body;

  // Ensure trial payment providers exist
  ensureTrialPaymentProvidersExist();

  // Find the Trial PayPal Button Embed provider
  const providerIndex = configStorage.payment.providers.findIndex(p => p.id === 'trial-paypal-button-embed');
  if (providerIndex === -1) {
    return res.status(404).json({
      message: 'Trial PayPal Button Embed provider not found'
    });
  }

  // Get the current buttons
  const config = configStorage.payment.providers[providerIndex].config || {};
  const buttons = Array.isArray((config as any).buttons) ? (config as any).buttons : [];

  // Generate a unique ID for the new button
  const buttonId = `trial-button-${Date.now()}`;

  // Add the new button
  buttons.push({
    id: buttonId,
    name: name || `Trial PayPal Button ${buttons.length + 1}`,
    buttonHtml: buttonHtml || '',
    description: description || '',
    active: active !== undefined ? active : true
  });

  // Update the config
  configStorage.payment.providers[providerIndex].config = {
    ...(config as any),
    buttons
  };

  console.log('Added new Trial PayPal button embed:',
    JSON.stringify(buttons[buttons.length - 1], null, 2));

  res.json({
    message: 'Trial PayPal button embed added successfully',
    buttonId,
    config: configStorage.payment
  });
});

// Edit a PayPal button embed
adminRouter.post('/payment-config/paypal-button-embed/edit', isAdmin, (req: Request, res: Response) => {
  const { id, name, buttonHtml, description, active } = req.body;

  // Find the PayPal Button Embed provider
  const providerIndex = configStorage.payment.providers.findIndex(p => p.id === 'paypal-button-embed');
  if (providerIndex === -1) {
    return res.status(404).json({
      message: 'PayPal Button Embed provider not found'
    });
  }

  // Get the current buttons
  const config = configStorage.payment.providers[providerIndex].config || {};
  const buttons = Array.isArray((config as any).buttons) ? (config as any).buttons : [];

  // Find the button to update
  const buttonIndex = buttons.findIndex((button: any) => button.id === id);
  if (buttonIndex === -1) {
    return res.status(404).json({
      message: `PayPal button with ID ${id} not found`
    });
  }

  // Update the button
  buttons[buttonIndex] = {
    ...buttons[buttonIndex],
    name: name !== undefined ? name : buttons[buttonIndex].name,
    buttonHtml: buttonHtml !== undefined ? buttonHtml : buttons[buttonIndex].buttonHtml,
    description: description !== undefined ? description : buttons[buttonIndex].description,
    active: active !== undefined ? active : buttons[buttonIndex].active
  };

  // Update the config
  configStorage.payment.providers[providerIndex].config = {
    ...(config as any),
    buttons
  };

  console.log('Updated PayPal button embed:',
    JSON.stringify(buttons[buttonIndex], null, 2));

  res.json({
    message: 'PayPal button embed updated successfully',
    config: configStorage.payment
  });
});

// Edit a Trial PayPal button embed
adminRouter.post('/payment-config/trial-paypal-button-embed/edit', isAdmin, (req: Request, res: Response) => {
  const { id, name, buttonHtml, description, active } = req.body;

  // Find the Trial PayPal Button Embed provider
  const providerIndex = configStorage.payment.providers.findIndex(p => p.id === 'trial-paypal-button-embed');
  if (providerIndex === -1) {
    return res.status(404).json({
      message: 'Trial PayPal Button Embed provider not found'
    });
  }

  // Get the current buttons
  const config = configStorage.payment.providers[providerIndex].config || {};
  const buttons = Array.isArray((config as any).buttons) ? (config as any).buttons : [];

  // Find the button to update
  const buttonIndex = buttons.findIndex((button: any) => button.id === id);
  if (buttonIndex === -1) {
    return res.status(404).json({
      message: `Trial PayPal button with ID ${id} not found`
    });
  }

  // Update the button
  buttons[buttonIndex] = {
    ...buttons[buttonIndex],
    name: name !== undefined ? name : buttons[buttonIndex].name,
    buttonHtml: buttonHtml !== undefined ? buttonHtml : buttons[buttonIndex].buttonHtml,
    description: description !== undefined ? description : buttons[buttonIndex].description,
    active: active !== undefined ? active : buttons[buttonIndex].active
  };

  // Update the config
  configStorage.payment.providers[providerIndex].config = {
    ...(config as any),
    buttons
  };

  console.log('Updated Trial PayPal button embed:',
    JSON.stringify(buttons[buttonIndex], null, 2));

  res.json({
    message: 'Trial PayPal button embed updated successfully',
    config: configStorage.payment
  });
});

// Delete a PayPal button embed
adminRouter.post('/payment-config/paypal-button-embed/delete', isAdmin, (req: Request, res: Response) => {
  const { id } = req.body;

  // Find the PayPal Button Embed provider
  const providerIndex = configStorage.payment.providers.findIndex(p => p.id === 'paypal-button-embed');
  if (providerIndex === -1) {
    return res.status(404).json({
      message: 'PayPal Button Embed provider not found'
    });
  }

  // Get the current buttons
  const config = configStorage.payment.providers[providerIndex].config || {};
  const buttons = Array.isArray((config as any).buttons) ? (config as any).buttons : [];

  // Find the button to delete
  const buttonIndex = buttons.findIndex((button: any) => button.id === id);
  if (buttonIndex === -1) {
    return res.status(404).json({
      message: `PayPal button with ID ${id} not found`
    });
  }

  // Remove the button
  buttons.splice(buttonIndex, 1);

  // Update the config
  configStorage.payment.providers[providerIndex].config = {
    ...(config as any),
    buttons
  };

  console.log('Deleted PayPal button embed with ID:', id);

  res.json({
    message: 'PayPal button embed deleted successfully',
    config: configStorage.payment
  });
});

// Delete a Trial PayPal button embed
adminRouter.post('/payment-config/trial-paypal-button-embed/delete', isAdmin, (req: Request, res: Response) => {
  const { id } = req.body;

  // Find the Trial PayPal Button Embed provider
  const providerIndex = configStorage.payment.providers.findIndex(p => p.id === 'trial-paypal-button-embed');
  if (providerIndex === -1) {
    return res.status(404).json({
      message: 'Trial PayPal Button Embed provider not found'
    });
  }

  // Get the current buttons
  const config = configStorage.payment.providers[providerIndex].config || {};
  const buttons = Array.isArray((config as any).buttons) ? (config as any).buttons : [];

  // Find the button to delete
  const buttonIndex = buttons.findIndex((button: any) => button.id === id);
  if (buttonIndex === -1) {
    return res.status(404).json({
      message: `Trial PayPal button with ID ${id} not found`
    });
  }

  // Remove the button
  buttons.splice(buttonIndex, 1);

  // Update the config
  configStorage.payment.providers[providerIndex].config = {
    ...(config as any),
    buttons
  };

  console.log('Deleted Trial PayPal button embed with ID:', id);

  res.json({
    message: 'Trial PayPal button embed deleted successfully',
    config: configStorage.payment
  });
});

// Add a new custom payment link
adminRouter.post('/payment-config/custom-link/add', isAdmin, (req: Request, res: Response) => {
  const { name, paymentLink, buttonText, successRedirectUrl, active } = req.body;

  // Find the Custom Link provider
  const providerIndex = configStorage.payment.providers.findIndex(p => p.id === 'custom-link');
  if (providerIndex === -1) {
    return res.status(404).json({
      message: 'Custom payment link provider not found'
    });
  }

  // Get the current links
  const config = configStorage.payment.providers[providerIndex].config || {};
  const links = Array.isArray((config as any).links) ? (config as any).links : [];

  // Generate a unique ID for the new link
  const linkId = `link-${Date.now()}`;

  // Add the new link
  links.push({
    id: linkId,
    name: name || `Payment Link ${links.length + 1}`,
    paymentLink: paymentLink || '',
    buttonText: buttonText || 'Pay Now',
    successRedirectUrl: successRedirectUrl || '',
    active: active !== undefined ? active : true
  });

  // Update the config
  configStorage.payment.providers[providerIndex].config = {
    ...(config as any),
    links
  };

  console.log('Added new custom payment link:',
    JSON.stringify(links[links.length - 1], null, 2));

  res.json({
    message: 'Custom payment link added successfully',
    linkId,
    config: configStorage.payment
  });
});

// Add a new trial custom payment link
adminRouter.post('/payment-config/trial-custom-link/add', isAdmin, (req: Request, res: Response) => {
  console.log('Received request to add trial custom payment link:', req.body);

  const { name, paymentLink, buttonText, successRedirectUrl, active } = req.body;

  // Ensure trial payment providers exist
  ensureTrialPaymentProvidersExist();

  // Find the Trial Custom Link provider
  const providerIndex = configStorage.payment.providers.findIndex(p => p.id === 'trial-custom-link');
  console.log('Trial custom link provider index:', providerIndex);

  if (providerIndex === -1) {
    console.error('Trial custom payment link provider not found');
    return res.status(404).json({
      message: 'Trial custom payment link provider not found'
    });
  }

  // Get the current links
  const config = configStorage.payment.providers[providerIndex].config || {};
  const links = Array.isArray((config as any).links) ? (config as any).links : [];

  // Generate a unique ID for the new link
  const linkId = `trial-link-${Date.now()}`;

  // Add the new link
  links.push({
    id: linkId,
    name: name || `Trial Payment Link ${links.length + 1}`,
    paymentLink: paymentLink || '',
    buttonText: buttonText || 'Start Trial',
    successRedirectUrl: successRedirectUrl || '',
    active: active !== undefined ? active : true
  });

  // Update the config
  configStorage.payment.providers[providerIndex].config = {
    ...(config as any),
    links
  };

  console.log('Added new trial custom payment link:',
    JSON.stringify(links[links.length - 1], null, 2));

  console.log('Updated trial custom link provider config:',
    JSON.stringify(configStorage.payment.providers[providerIndex].config, null, 2));

  console.log('Sending response for trial custom payment link add');
  res.json({
    message: 'Trial custom payment link added successfully',
    linkId,
    config: configStorage.payment
  });
  console.log('Response sent for trial custom payment link add');
});

// Test endpoint
adminRouter.post('/test-endpoint', (req: Request, res: Response) => {
  console.log('Test endpoint called with body:', req.body);
  res.json({
    message: 'Test endpoint called successfully',
    receivedData: req.body
  });
});

// Test endpoint for trial links that doesn't require authentication
adminRouter.post('/test-trial-link', (req: Request, res: Response) => {
  console.log('Test trial link endpoint called with body:', req.body);

  const { name, paymentLink, buttonText, successRedirectUrl, active } = req.body;

  // Ensure trial payment providers exist
  ensureTrialPaymentProvidersExist();

  // Find the Trial Custom Link provider
  const providerIndex = configStorage.payment.providers.findIndex(p => p.id === 'trial-custom-link');
  console.log('Trial custom link provider index:', providerIndex);

  if (providerIndex === -1) {
    console.error('Trial custom payment link provider not found');
    return res.status(404).json({
      message: 'Trial custom payment link provider not found'
    });
  }

  // Get the current links
  const config = configStorage.payment.providers[providerIndex].config || {};
  const links = Array.isArray((config as any).links) ? (config as any).links : [];

  // Generate a unique ID for the new link
  const linkId = `trial-link-${Date.now()}`;

  // Add the new link
  links.push({
    id: linkId,
    name: name || `Trial Payment Link ${links.length + 1}`,
    paymentLink: paymentLink || '',
    buttonText: buttonText || 'Start Trial',
    successRedirectUrl: successRedirectUrl || '',
    active: active !== undefined ? active : true
  });

  // Update the config
  configStorage.payment.providers[providerIndex].config = {
    ...(config as any),
    links
  };

  console.log('Added new trial custom payment link (test endpoint):',
    JSON.stringify(links[links.length - 1], null, 2));

  console.log('Updated trial custom link provider config (test endpoint):',
    JSON.stringify(configStorage.payment.providers[providerIndex].config, null, 2));

  console.log('Sending response for trial custom payment link add (test endpoint)');
  res.json({
    message: 'Trial custom payment link added successfully',
    linkId,
    config: configStorage.payment
  });
  console.log('Response sent for trial custom payment link add (test endpoint)');
});

// Alternative endpoint for adding trial custom payment links
adminRouter.post('/payment-config/add-trial-link', isAdmin, (req: Request, res: Response) => {
  console.log('Received request to add trial custom payment link (alternative endpoint):', req.body);

  const { name, paymentLink, buttonText, successRedirectUrl, active } = req.body;

  // Ensure trial payment providers exist
  ensureTrialPaymentProvidersExist();

  // Find the Trial Custom Link provider
  const providerIndex = configStorage.payment.providers.findIndex(p => p.id === 'trial-custom-link');
  console.log('Trial custom link provider index:', providerIndex);

  if (providerIndex === -1) {
    console.error('Trial custom payment link provider not found');
    return res.status(404).json({
      message: 'Trial custom payment link provider not found'
    });
  }

  // Get the current links
  const config = configStorage.payment.providers[providerIndex].config || {};
  const links = Array.isArray((config as any).links) ? (config as any).links : [];

  // Generate a unique ID for the new link
  const linkId = `trial-link-${Date.now()}`;

  // Add the new link
  links.push({
    id: linkId,
    name: name || `Trial Payment Link ${links.length + 1}`,
    paymentLink: paymentLink || '',
    buttonText: buttonText || 'Start Trial',
    successRedirectUrl: successRedirectUrl || '',
    active: active !== undefined ? active : true
  });

  // Update the config
  configStorage.payment.providers[providerIndex].config = {
    ...(config as any),
    links
  };

  console.log('Added new trial custom payment link (alternative endpoint):',
    JSON.stringify(links[links.length - 1], null, 2));

  console.log('Updated trial custom link provider config (alternative endpoint):',
    JSON.stringify(configStorage.payment.providers[providerIndex].config, null, 2));

  console.log('Sending response for trial custom payment link add (alternative endpoint)');
  res.json({
    message: 'Trial custom payment link added successfully',
    linkId,
    config: configStorage.payment
  });
  console.log('Response sent for trial custom payment link add (alternative endpoint)');
});

// Update a specific custom payment link
adminRouter.post('/payment-config/custom-link/:id', isAdmin, (req: Request, res: Response) => {
  const { id } = req.params;
  const { name, paymentLink, buttonText, successRedirectUrl, active } = req.body;

  // Find the Custom Link provider
  const providerIndex = configStorage.payment.providers.findIndex(p => p.id === 'custom-link');
  if (providerIndex === -1) {
    return res.status(404).json({
      message: 'Custom payment link provider not found'
    });
  }

  // Get the current links
  const config = configStorage.payment.providers[providerIndex].config || {};
  const links = Array.isArray((config as any).links) ? (config as any).links : [];

  // Find the link to update
  const linkIndex = links.findIndex((link: any) => link.id === id);
  if (linkIndex === -1) {
    return res.status(404).json({
      message: `Payment link with ID ${id} not found`
    });
  }

  // Update the link
  links[linkIndex] = {
    ...links[linkIndex],
    name: name !== undefined ? name : links[linkIndex].name,
    paymentLink: paymentLink !== undefined ? paymentLink : links[linkIndex].paymentLink,
    buttonText: buttonText !== undefined ? buttonText : links[linkIndex].buttonText,
    successRedirectUrl: successRedirectUrl !== undefined ? successRedirectUrl : links[linkIndex].successRedirectUrl,
    active: active !== undefined ? active : links[linkIndex].active
  };

  // Update the config
  configStorage.payment.providers[providerIndex].config = {
    ...(config as any),
    links
  };

  console.log('Updated custom payment link:',
    JSON.stringify(links[linkIndex], null, 2));

  res.json({
    message: 'Custom payment link updated successfully',
    config: configStorage.payment
  });
});

// Update a specific trial custom payment link
adminRouter.post('/payment-config/trial-custom-link/:id', isAdmin, (req: Request, res: Response) => {
  const { id } = req.params;
  const { name, paymentLink, buttonText, successRedirectUrl, active } = req.body;

  console.log(`Updating trial custom payment link with ID: ${id}`, req.body);

  // Ensure trial payment providers exist
  ensureTrialPaymentProvidersExist();

  // Find the Trial Custom Link provider
  const providerIndex = configStorage.payment.providers.findIndex(p => p.id === 'trial-custom-link');
  if (providerIndex === -1) {
    return res.status(404).json({
      message: 'Trial custom payment link provider not found'
    });
  }

  // Get the current links
  const config = configStorage.payment.providers[providerIndex].config || {};
  const links = Array.isArray((config as any).links) ? (config as any).links : [];

  // Find the link to update
  const linkIndex = links.findIndex((link: any) => link.id === id);
  if (linkIndex === -1) {
    return res.status(404).json({
      message: `Trial payment link with ID ${id} not found`
    });
  }

  // Update the link
  links[linkIndex] = {
    ...links[linkIndex],
    name: name !== undefined ? name : links[linkIndex].name,
    paymentLink: paymentLink !== undefined ? paymentLink : links[linkIndex].paymentLink,
    buttonText: buttonText !== undefined ? buttonText : links[linkIndex].buttonText,
    successRedirectUrl: successRedirectUrl !== undefined ? successRedirectUrl : links[linkIndex].successRedirectUrl,
    active: active !== undefined ? active : links[linkIndex].active
  };

  // Update the config
  configStorage.payment.providers[providerIndex].config = {
    ...(config as any),
    links
  };

  console.log('Updated trial custom payment link:',
    JSON.stringify(links[linkIndex], null, 2));

  res.json({
    message: 'Trial custom payment link updated successfully',
    config: configStorage.payment
  });
});

// Delete a custom payment link
adminRouter.delete('/payment-config/custom-link/:id', isAdmin, (req: Request, res: Response) => {
  const { id } = req.params;

  // Find the Custom Link provider
  const providerIndex = configStorage.payment.providers.findIndex(p => p.id === 'custom-link');
  if (providerIndex === -1) {
    return res.status(404).json({
      message: 'Custom payment link provider not found'
    });
  }

  // Get the current links
  const config = configStorage.payment.providers[providerIndex].config || {};
  const links = Array.isArray((config as any).links) ? (config as any).links : [];

  // Find the link to delete
  const linkIndex = links.findIndex((link: any) => link.id === id);
  if (linkIndex === -1) {
    return res.status(404).json({
      message: `Payment link with ID ${id} not found`
    });
  }

  // Remove the link
  links.splice(linkIndex, 1);

  // Update the config
  configStorage.payment.providers[providerIndex].config = {
    ...(config as any),
    links
  };

  console.log('Deleted custom payment link with ID:', id);

  res.json({
    message: 'Custom payment link deleted successfully',
    config: configStorage.payment
  });
});

// Delete a trial custom payment link
adminRouter.delete('/payment-config/trial-custom-link/:id', isAdmin, (req: Request, res: Response) => {
  const { id } = req.params;

  // Find the Trial Custom Link provider
  const providerIndex = configStorage.payment.providers.findIndex(p => p.id === 'trial-custom-link');
  if (providerIndex === -1) {
    return res.status(404).json({
      message: 'Trial custom payment link provider not found'
    });
  }

  // Get the current links
  const config = configStorage.payment.providers[providerIndex].config || {};
  const links = Array.isArray((config as any).links) ? (config as any).links : [];

  // Find the link to delete
  const linkIndex = links.findIndex((link: any) => link.id === id);
  if (linkIndex === -1) {
    return res.status(404).json({
      message: `Trial payment link with ID ${id} not found`
    });
  }

  // Remove the link
  links.splice(linkIndex, 1);

  // Update the config
  configStorage.payment.providers[providerIndex].config = {
    ...(config as any),
    links
  };

  console.log('Deleted trial custom payment link with ID:', id);

  res.json({
    message: 'Trial custom payment link deleted successfully',
    config: configStorage.payment
  });
});

adminRouter.post('/payment-config/test', isAdmin, async (req: Request, res: Response) => {
  const { provider, config, testEmail } = req.body;

  if (!testEmail) {
    return res.status(400).json({ message: 'Test email is required' });
  }

  // Different validation based on provider
  if (provider === 'paypal') {
    if (!config || !config.clientId || !config.clientSecret) {
      return res.status(400).json({ message: 'Client ID and Secret are required for PayPal' });
    }
  } else if (provider === 'custom-link' || provider === 'trial-custom-link') {
    if (!config || !config.paymentLink || !config.buttonText) {
      return res.status(400).json({ message: 'Payment link and button text are required for Custom Payment Link' });
    }
  } else if (provider === 'paypal-button-embed' || provider === 'trial-paypal-button-embed') {
    if (!config || !config.buttonHtml) {
      return res.status(400).json({ message: 'Button HTML is required for PayPal Button Embed' });
    }
  } else {
    return res.status(400).json({ message: 'Unsupported payment provider' });
  }

  try {
    if (provider === 'paypal') {
      // Import dynamically to avoid circular dependencies
      const { testPayPalConnection } = await import('../services/paypal');
      const { testPayPalInvoiceConnection, createPayPalInvoice } = await import('../services/paypal-invoice');

      // First test the regular PayPal connection
      console.log('Testing PayPal connection with provided credentials...');
      const connectionSuccess = await testPayPalConnection(config);

      if (!connectionSuccess) {
        return res.status(400).json({
          message: 'Failed to connect to PayPal API. Please check your credentials.',
          success: false
        });
      }

      // Then test the PayPal Invoice connection
      console.log('Testing PayPal Invoice connection with provided credentials...');
      const invoiceConnectionSuccess = await testPayPalInvoiceConnection(config);

      if (!invoiceConnectionSuccess) {
        return res.status(400).json({
          message: 'Failed to connect to PayPal Invoicing API. Please check your credentials.',
          success: false
        });
      }

      console.log('PayPal connections test successful. Generating test invoice...');

      // Save the PayPal settings temporarily for this test
      const paypalProvider = configStorage.payment.providers.find(p => p.id === 'paypal');
      if (paypalProvider) {
        // Make sure we preserve the PayPal email if it's not provided in the test config
        const paypalEmail = config.paypalEmail || paypalProvider.config.paypalEmail || '<EMAIL>';

        paypalProvider.config = {
          ...config,
          paypalEmail // Ensure the PayPal email is included
        };
        paypalProvider.active = true;

        console.log('Using PayPal email for test:', paypalEmail);
      }
    } else if (provider === 'custom-link') {
      // Import the custom link service
      const { testCustomLinkConnection } = await import('../services/custom-link');

      // Test the custom payment link
      console.log('Testing custom payment link with provided configuration...');
      const connectionSuccess = await testCustomLinkConnection(config);

      if (!connectionSuccess) {
        return res.status(400).json({
          message: 'Invalid custom payment link configuration. Please check your settings.',
          success: false
        });
      }

      console.log('Custom payment link test successful.');
    } else if (provider === 'paypal-button-embed') {
      // Import the PayPal button embed service
      const { testPayPalButtonEmbedConnection } = await import('../services/paypal-button-embed');

      // Test the PayPal button embed
      console.log('Testing PayPal button embed with provided configuration...');
      const connectionSuccess = await testPayPalButtonEmbedConnection(config);

      if (!connectionSuccess) {
        return res.status(400).json({
          message: 'Invalid PayPal button embed configuration. Please check your settings.',
          success: false
        });
      }

      console.log('PayPal button embed test successful.');

      // Save the PayPal button embed settings temporarily for this test
      const paypalButtonEmbedProvider = configStorage.payment.providers.find(p => p.id === 'paypal-button-embed');
      if (paypalButtonEmbedProvider) {
        // Create a temporary button for testing
        paypalButtonEmbedProvider.config = {
          buttons: [
            {
              id: 'test-button',
              name: 'Test Button',
              buttonHtml: config.buttonHtml,
              description: 'Test button for email',
              active: true
            }
          ],
          rotationMethod: 'round-robin',
          lastUsedIndex: -1
        };

        paypalButtonEmbedProvider.active = true;
        console.log('Using PayPal button embed for test');
      }

      // Save the custom link settings temporarily for this test
      const customLinkProvider = configStorage.payment.providers.find(p => p.id === 'custom-link');
      if (customLinkProvider) {
        // If testing a specific link (from the links array)
        if (config.linkId) {
          // Find the link in the current configuration
          const currentConfig = customLinkProvider.config as any;
          const links = Array.isArray(currentConfig.links) ? currentConfig.links : [];
          const linkIndex = links.findIndex((link: any) => link.id === config.linkId);

          if (linkIndex !== -1) {
            // Update just this link for testing
            links[linkIndex] = {
              ...links[linkIndex],
              paymentLink: config.paymentLink || links[linkIndex].paymentLink,
              buttonText: config.buttonText || links[linkIndex].buttonText,
              successRedirectUrl: config.successRedirectUrl || links[linkIndex].successRedirectUrl,
              active: true
            };

            // Make sure other links are inactive for this test
            links.forEach((link: any, idx: number) => {
              if (idx !== linkIndex) {
                link.active = false;
              }
            });

            customLinkProvider.config = {
              ...currentConfig,
              links,
              lastUsedIndex: linkIndex - 1 // So the test will use this link
            };

            console.log(`Using custom payment link "${links[linkIndex].name}" for test:`, links[linkIndex].paymentLink);
          } else {
            console.log(`Link with ID ${config.linkId} not found, using default configuration`);
          }
        }
        // If testing the entire configuration
        else if (config.links && Array.isArray(config.links)) {
          customLinkProvider.config = {
            ...config
          };
          console.log('Using custom payment links configuration for test');
        }
        // If testing a single link (legacy format)
        else if (config.paymentLink) {
          // Create a temporary link for testing
          customLinkProvider.config = {
            links: [
              {
                id: 'test-link',
                name: 'Test Link',
                paymentLink: config.paymentLink,
                buttonText: config.buttonText || 'Pay Now',
                successRedirectUrl: config.successRedirectUrl || '',
                active: true
              }
            ],
            rotationMethod: 'round-robin',
            lastUsedIndex: -1
          };
          console.log('Using single custom payment link for test:', config.paymentLink);
        }

        customLinkProvider.active = true;
      }
    }

    const testCustomerData = {
      fullName: 'Test Customer',
      email: testEmail,
      productId: 0
    };

    const testProduct = {
      id: 0,
      name: 'Test Product',
      description: 'This is a test product for PayPal invoice generation',
      price: '1.00',
      imageUrl: '',
      active: true
    };

    // Generate a test invoice based on the provider
    let invoiceResult;

    if (provider === 'paypal') {
      // Test by generating a real gift invoice using the PayPal Invoicing API
      invoiceResult = await createPayPalInvoice(testCustomerData, testProduct);
    } else if (provider === 'custom-link' || provider === 'trial-custom-link') {
      // Import the custom link service
      const { createCustomPaymentLink } = await import('../services/custom-link');

      // Generate a test custom payment link
      const isTrial = provider === 'trial-custom-link';
      console.log(`Creating ${isTrial ? 'trial' : 'regular'} custom payment link for test...`);

      // If testing a specific link, make sure we use the correct provider
      if (isTrial && config.linkId) {
        // Find the link in the trial custom link provider
        const trialCustomLinkProvider = configStorage.payment.providers.find(p => p.id === 'trial-custom-link');
        if (trialCustomLinkProvider) {
          const trialConfig = trialCustomLinkProvider.config as any;
          const trialLinks = Array.isArray(trialConfig.links) ? trialConfig.links : [];
          const linkIndex = trialLinks.findIndex((link: any) => link.id === config.linkId);

          if (linkIndex !== -1) {
            console.log(`Found trial link with ID ${config.linkId} for testing`);
            trialCustomLinkProvider.active = true;
          } else {
            console.log(`Trial link with ID ${config.linkId} not found`);
          }
        }
      }

      invoiceResult = await createCustomPaymentLink(testCustomerData, testProduct, isTrial);
    } else if (provider === 'paypal-button-embed' || provider === 'trial-paypal-button-embed') {
      // Import the PayPal button embed service
      const { createPayPalButtonEmbed } = await import('../services/paypal-button-embed');

      // Generate a test PayPal button embed
      const isTrial = provider === 'trial-paypal-button-embed';
      console.log(`Creating ${isTrial ? 'trial' : 'regular'} PayPal button embed for test...`);

      // If testing a specific button, make sure we use the correct provider
      if (isTrial && config.buttonId) {
        // Find the button in the trial PayPal button embed provider
        const trialPaypalButtonEmbedProvider = configStorage.payment.providers.find(p => p.id === 'trial-paypal-button-embed');
        if (trialPaypalButtonEmbedProvider) {
          const trialConfig = trialPaypalButtonEmbedProvider.config as any;
          const trialButtons = Array.isArray(trialConfig.buttons) ? trialConfig.buttons : [];
          const buttonIndex = trialButtons.findIndex((button: any) => button.id === config.buttonId);

          if (buttonIndex !== -1) {
            console.log(`Found trial button with ID ${config.buttonId} for testing`);
            trialPaypalButtonEmbedProvider.active = true;
          } else {
            console.log(`Trial button with ID ${config.buttonId} not found`);
          }
        }
      }

      invoiceResult = await createPayPalButtonEmbed(testCustomerData, testProduct, isTrial);
    } else {
      throw new Error(`Unsupported payment provider: ${provider}`);
    }

    const { id, url, isSimulated, error, isDraft, status, noPayPalAccount } = invoiceResult;

    if (provider === 'custom-link' || provider === 'trial-custom-link') {
      try {
        // Send a test email with the custom payment link
        const { sendInvoiceEmail } = await import('../services/email');
        await sendInvoiceEmail(testCustomerData, testProduct, url, provider);
        console.log(`Test email sent to ${testEmail} with ${provider} payment link`);

        // Return success response
        res.json({
          message: `${provider === 'trial-custom-link' ? 'Trial custom' : 'Custom'} payment link generated successfully. Test email sent to ${testEmail}.`,
          success: true,
          invoiceId: id,
          invoiceUrl: url,
          status: 'pending'
        });
      } catch (emailError) {
        console.error('Error sending test email:', emailError);
        res.json({
          message: `${provider === 'trial-custom-link' ? 'Trial custom' : 'Custom'} payment link generated successfully, but failed to send test email: ${emailError.message}`,
          success: true,
          invoiceId: id,
          invoiceUrl: url,
          status: 'pending',
          emailError: emailError.message
        });
      }
    } else if (provider === 'paypal-button-embed' || provider === 'trial-paypal-button-embed') {
      try {
        // Send a test email with the PayPal button embed
        const { sendInvoiceEmail } = await import('../services/email');
        await sendInvoiceEmail(testCustomerData, testProduct, invoiceResult, provider);
        console.log(`Test email sent to ${testEmail} with ${provider} button embed`);

        // Return success response
        res.json({
          message: `${provider === 'trial-paypal-button-embed' ? 'Trial PayPal' : 'PayPal'} button embed generated successfully. Test email sent to ${testEmail}.`,
          success: true,
          invoiceId: id,
          status: 'pending'
        });
      } catch (emailError) {
        console.error('Error sending test email:', emailError);
        res.json({
          message: `${provider === 'trial-paypal-button-embed' ? 'Trial PayPal' : 'PayPal'} button embed generated successfully, but failed to send test email: ${emailError.message}`,
          success: true,
          invoiceId: id,
          status: 'pending',
          emailError: emailError.message
        });
      }
    } else if (noPayPalAccount) {
      res.json({
        message: `Connection to ${provider} API successful, but the test email is not valid.`,
        success: true,
        invoiceId: id,
        invoiceUrl: url,
        noPayPalAccount: true
      });
    } else if (isSimulated) {
      res.json({
        message: `Connection to ${provider} API successful, but invoice generation failed: ${error}`,
        success: false,
        invoiceId: id,
        invoiceUrl: url,
        isSimulated: true,
        error
      });
    } else if (isDraft) {
      res.json({
        message: `Connection to ${provider} API successful. Invoice created in draft status (${status}).`,
        success: true,
        invoiceId: id,
        invoiceUrl: url,
        isDraft: true,
        status: status
      });
    } else {
      res.json({
        message: `Connection to ${provider} API successful. Invoice generated and sent.`,
        success: true,
        invoiceId: id,
        invoiceUrl: url,
        status: status
      });
    }
  } catch (error) {
    console.error('Error testing PayPal invoice generation:', error);
    res.status(500).json({
      message: 'Failed to test PayPal invoice generation',
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Mock image upload endpoint
// In a real app, this would use a file storage service like AWS S3
adminRouter.post('/upload-image', isAdmin, (req: Request, res: Response) => {
  try {
    const { imageData } = req.body;

    if (!imageData || !imageData.startsWith('data:image/')) {
      return res.status(400).json({ message: 'Valid image data is required' });
    }

    // Generate a random filename
    const randomId = randomBytes(16).toString('hex');
    const imageType = imageData.split(';')[0].split('/')[1];
    const filename = `product-${randomId}.${imageType}`;

    // In a real app, we would save the image to disk or a cloud storage service here
    // For this demo, we'll just pretend we saved it and return a fake URL

    // Return a success response with the mockup image URL
    res.status(200).json({
      url: `https://images.unsplash.com/photo-${randomId}?w=500&q=80`,
      filename
    });
  } catch (error) {
    console.error('Error processing image upload:', error);
    res.status(500).json({ message: 'Failed to process image upload' });
  }
});

export default adminRouter;