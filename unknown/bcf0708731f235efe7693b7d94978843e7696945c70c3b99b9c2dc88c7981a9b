<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Browser Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            margin: 0;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        .test-box {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .success {
            background: rgba(0,255,0,0.2);
            border: 2px solid #00ff00;
        }
        .error {
            background: rgba(255,0,0,0.2);
            border: 2px solid #ff0000;
        }
        button {
            background: #4CAF50;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        button:hover {
            background: #45a049;
        }
        #results {
            text-align: left;
            background: rgba(0,0,0,0.3);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Browser Diagnostic Test</h1>
        <p>This page tests if basic browser functionality is working</p>
        
        <div class="test-box success">
            <h2>✅ HTML is Working</h2>
            <p>If you can see this page, HTML rendering is functional</p>
        </div>

        <div class="test-box" id="js-test">
            <h2>⏳ JavaScript Test</h2>
            <p>Testing JavaScript execution...</p>
        </div>

        <div class="test-box" id="fetch-test">
            <h2>⏳ API Test</h2>
            <p>Testing API connectivity...</p>
        </div>

        <button onclick="runTests()">🔄 Run All Tests</button>
        <button onclick="testReactApp()">🚀 Test React App</button>

        <div id="results"></div>
    </div>

    <script>
        // Test 1: Basic JavaScript
        function testJavaScript() {
            const jsTest = document.getElementById('js-test');
            try {
                jsTest.className = 'test-box success';
                jsTest.innerHTML = '<h2>✅ JavaScript is Working</h2><p>JavaScript execution is functional</p>';
                return true;
            } catch (error) {
                jsTest.className = 'test-box error';
                jsTest.innerHTML = '<h2>❌ JavaScript Failed</h2><p>Error: ' + error.message + '</p>';
                return false;
            }
        }

        // Test 2: API Connectivity
        async function testAPI() {
            const fetchTest = document.getElementById('fetch-test');
            try {
                fetchTest.innerHTML = '<h2>⏳ Testing API...</h2><p>Fetching from /api/system-messages</p>';
                
                const response = await fetch('/api/system-messages');
                const data = await response.json();
                
                fetchTest.className = 'test-box success';
                fetchTest.innerHTML = '<h2>✅ API is Working</h2><p>Successfully fetched ' + data.length + ' system messages</p>';
                return true;
            } catch (error) {
                fetchTest.className = 'test-box error';
                fetchTest.innerHTML = '<h2>❌ API Failed</h2><p>Error: ' + error.message + '</p>';
                return false;
            }
        }

        // Test 3: React App
        function testReactApp() {
            const results = document.getElementById('results');
            results.innerHTML = '<h3>🚀 Testing React App...</h3>';
            
            // Try to load React
            const script = document.createElement('script');
            script.src = '/src/main.tsx';
            script.type = 'module';
            script.onload = function() {
                results.innerHTML += '<p>✅ React script loaded successfully</p>';
            };
            script.onerror = function() {
                results.innerHTML += '<p>❌ React script failed to load</p>';
            };
            document.head.appendChild(script);
        }

        // Run all tests
        async function runTests() {
            const results = document.getElementById('results');
            results.innerHTML = '<h3>🔍 Running Diagnostic Tests...</h3>';
            
            // Test JavaScript
            const jsWorking = testJavaScript();
            results.innerHTML += '<p>' + (jsWorking ? '✅' : '❌') + ' JavaScript: ' + (jsWorking ? 'Working' : 'Failed') + '</p>';
            
            // Test API
            const apiWorking = await testAPI();
            results.innerHTML += '<p>' + (apiWorking ? '✅' : '❌') + ' API: ' + (apiWorking ? 'Working' : 'Failed') + '</p>';
            
            // Browser info
            results.innerHTML += '<h3>🌐 Browser Information:</h3>';
            results.innerHTML += '<p><strong>User Agent:</strong> ' + navigator.userAgent + '</p>';
            results.innerHTML += '<p><strong>URL:</strong> ' + window.location.href + '</p>';
            results.innerHTML += '<p><strong>Timestamp:</strong> ' + new Date().toISOString() + '</p>';
            
            // Console check
            results.innerHTML += '<h3>📝 Console Check:</h3>';
            results.innerHTML += '<p>Check browser console (F12) for any error messages</p>';
        }

        // Auto-run tests when page loads
        window.onload = function() {
            setTimeout(runTests, 1000);
        };
    </script>
</body>
</html>
