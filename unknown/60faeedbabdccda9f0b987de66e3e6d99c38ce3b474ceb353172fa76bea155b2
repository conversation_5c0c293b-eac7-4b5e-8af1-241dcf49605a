# Digital Invoice & IPTV Subscription Management System

A comprehensive full-stack application for managing IPTV subscriptions, digital invoices, and payment processing with advanced email automation and multi-database support.

## 🏗️ **APPLICATION ARCHITECTURE**

### **Technology Stack**
- **Frontend**: React 18 + TypeScript + Vite
- **Backend**: Node.js + Express + TypeScript
- **Database**: Multi-database support (MySQL, PostgreSQL, SQLite)
- **ORM**: Drizzle ORM with automatic migrations
- **UI Framework**: Tailwind CSS + Radix UI + Shadcn/ui
- **Email**: Nodemailer with multiple SMTP providers
- **Payment**: PayPal SDK + Custom payment links
- **Authentication**: Passport.js with session management
- **File Uploads**: Multer with local storage
- **Build Tool**: Vite + ESBuild for production

### **Key Features**
- 🎯 **Custom Checkout Pages** - Create branded checkout experiences
- 📧 **Multi-SMTP Email System** - Automated email delivery with failover
- 💳 **Payment Processing** - PayPal integration + custom payment links
- 🔐 **Admin Dashboard** - Complete management interface
- 📊 **Analytics & Reporting** - Order tracking and conversion metrics
- 🤖 **Telegram Integration** - Real-time notifications
- 🌐 **Multi-Database Support** - MySQL, PostgreSQL, SQLite
- 📱 **Responsive Design** - Mobile-first approach
- 🔒 **Security Features** - 2FA, session management, IP tracking

## 📁 **PROJECT STRUCTURE**

```
Digital Invoice 60/
├── client/                     # Frontend React application
│   ├── src/
│   │   ├── components/         # Reusable UI components
│   │   │   ├── admin/         # Admin-specific components
│   │   │   ├── ui/            # Base UI components (Shadcn/ui)
│   │   │   └── templates/     # Email template components
│   │   ├── pages/             # Application pages
│   │   │   ├── admin/         # Admin dashboard pages
│   │   │   └── checkout/      # Checkout flow pages
│   │   ├── hooks/             # Custom React hooks
│   │   ├── lib/               # Utility functions and API clients
│   │   └── api/               # API service functions
│   ├── public/                # Static assets
│   └── index.html             # Main HTML template
├── server/                     # Backend Express application
│   ├── routes/                # API route handlers
│   │   ├── admin.ts           # Admin management routes
│   │   ├── custom-checkout.ts # Checkout page handling
│   │   ├── invoices.ts        # Invoice management
│   │   ├── email-templates.ts # Email template management
│   │   └── ...               # Other route files
│   ├── services/              # Business logic services
│   │   ├── email.ts           # Email sending service
│   │   ├── paypal.ts          # PayPal integration
│   │   ├── telegram-bot.ts    # Telegram notifications
│   │   └── system-monitor.ts  # System monitoring
│   ├── middleware/            # Express middleware
│   │   ├── auth.ts            # Authentication middleware
│   │   └── privacy.ts         # Privacy protection
│   ├── migrations/            # Database migration files
│   ├── utils/                 # Utility functions
│   └── index.ts               # Main server entry point
├── shared/                     # Shared code between client/server
│   ├── schema.ts              # Database schema definitions
│   ├── types.ts               # TypeScript type definitions
│   └── email-templates.ts     # Email template definitions
├── dist/                       # Production build output
├── uploads/                    # File upload storage
├── backups/                    # Database backup files
└── node_modules/              # Dependencies
```

## 🗄️ **DATABASE SCHEMA**

### **Core Tables**

#### **users**
- `id` (Primary Key)
- `username` (Unique)
- `password` (Hashed)

#### **products**
- `id` (Primary Key)
- `name` - Product name
- `description` - Product description
- `price` - Product price (Decimal)
- `imageUrl` - Product image URL
- `active` - Product status (Boolean)

#### **invoices**
- `id` (Primary Key)
- `customerName` - Customer full name
- `customerEmail` - Customer email address
- `productId` - Foreign key to products
- `amount` - Invoice amount (Decimal)
- `status` - Payment status (pending/paid/sent)
- `paypalInvoiceId` - PayPal invoice identifier
- `paypalInvoiceUrl` - Payment URL
- `isTrialOrder` - Trial order flag (Boolean)
- `hasUpgraded` - Upgrade status (Boolean)
- `upgradedAt` - Upgrade timestamp
- `createdAt` - Creation timestamp
- `customCheckoutPageId` - Foreign key to checkout pages
- `country` - Customer country
- `appType` - IPTV application type
- `macAddress` - Device MAC address

#### **custom_checkout_pages**
- `id` (Primary Key)
- `title` - Page title
- `slug` - URL slug (Unique)
- `productName` - Product name
- `productDescription` - Product description
- `price` - Product price (Decimal)
- `imageUrl` - Product image
- `paymentMethod` - Payment method type
- `customPaymentLinkId` - Payment link reference
- `paypalButtonId` - PayPal button reference
- `embedCodeId` - Embed code reference
- `requireAllowedEmail` - Email restriction flag
- `isTrialCheckout` - Trial checkout flag
- `confirmationMessage` - Success message (HTML)
- `headerTitle` - Page header title
- `footerText` - Page footer text
- `headerLogo` - Header logo URL
- `footerLogo` - Footer logo URL
- `themeMode` - Theme (light/dark)
- `useReferrerMasking` - Privacy protection flag
- `redirectDelay` - Redirect delay (milliseconds)
- `expiresAt` - Page expiration date
- `active` - Page status
- `views` - Page view count
- `conversions` - Conversion count
- `createdAt` - Creation timestamp
- `updatedAt` - Last update timestamp

#### **smtp_providers**
- `id` (Primary Key, String)
- `name` - Provider name
- `host` - SMTP host
- `port` - SMTP port
- `secure` - SSL/TLS flag
- `username` - SMTP username
- `password` - SMTP password
- `fromEmail` - Sender email
- `fromName` - Sender name
- `active` - Provider status
- `isDefault` - Default provider flag
- `isBackup` - Backup provider flag
- `createdAt` - Creation timestamp
- `updatedAt` - Last update timestamp

#### **email_templates**
- `id` (Primary Key)
- `templateId` - Template identifier (Unique)
- `name` - Template name
- `description` - Template description
- `subject` - Email subject
- `htmlContent` - HTML email content
- `textContent` - Plain text content
- `content` - Legacy content field
- `category` - Template category
- `isDefault` - Default template flag
- `createdAt` - Creation timestamp
- `updatedAt` - Last update timestamp

#### **allowed_emails**
- `id` (Primary Key)
- `email` - Email address (Unique)
- `notes` - Admin notes
- `smtpProvider` - Associated SMTP provider
- `lastUpdated` - Last update timestamp
- `createdAt` - Creation timestamp

#### **paypal_buttons**
- `id` (Primary Key)
- `name` - Button name
- `buttonCode` - HTML button code
- `description` - Button description
- `active` - Button status
- `createdAt` - Creation timestamp
- `updatedAt` - Last update timestamp

#### **custom_invoices**
- `id` (Primary Key)
- `invoiceNumber` - Invoice number (Unique)
- `customerName` - Customer name
- `customerEmail` - Customer email
- `amount` - Invoice amount (Decimal)
- `currency` - Currency code (Default: USD)
- `description` - Invoice description
- `paypalButtonId` - PayPal button reference
- `status` - Invoice status
- `dueDate` - Payment due date
- `viewCount` - View counter
- `paidAt` - Payment timestamp
- `createdAt` - Creation timestamp
- `updatedAt` - Last update timestamp

#### **system_messages**
- `id` (Primary Key)
- `messageId` - Message identifier (Unique)
- `category` - Message category
- `name` - Message name
- `description` - Message description
- `content` - Message content
- `isHtml` - HTML content flag
- `createdAt` - Creation timestamp
- `updatedAt` - Last update timestamp

### **Database Support**
The application supports three database types:
- **MySQL** (Recommended for production)
- **PostgreSQL** (Alternative for production)
- **SQLite** (Development/testing only)

Database type is automatically detected from the `DATABASE_URL` environment variable.

## 🚀 **CLOUDPANEL.IO HOSTING REQUIREMENTS**

### **System Requirements**
- **OS**: Ubuntu 20.04+ or Debian 11+
- **RAM**: Minimum 2GB (4GB+ recommended)
- **Storage**: Minimum 20GB SSD
- **CPU**: 2+ cores recommended
- **Node.js**: Version 18+ (LTS recommended)
- **Database**: MySQL 8.0+ or PostgreSQL 13+

### **CloudPanel Setup Steps**

#### **1. Server Preparation**
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js 18+ (if not already installed)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify installation
node --version  # Should be 18+
npm --version
```

#### **2. Database Setup**
```bash
# Create MySQL database and user
mysql -u root -p
CREATE DATABASE digital_invoice_db;
CREATE USER 'invoice_user'@'localhost' IDENTIFIED BY 'secure_password_here';
GRANT ALL PRIVILEGES ON digital_invoice_db.* TO 'invoice_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

#### **3. Application Deployment**
```bash
# Create application directory
sudo mkdir -p /var/www/digital-invoice
sudo chown www-data:www-data /var/www/digital-invoice

# Upload application files to /var/www/digital-invoice/
# (Use FTP, Git, or file manager)

# Set proper permissions
sudo chown -R www-data:www-data /var/www/digital-invoice
sudo chmod -R 755 /var/www/digital-invoice
```

#### **4. Environment Configuration**
Create `.env.production` file in the application root:
```env
# Database Configuration
DATABASE_URL=mysql://invoice_user:secure_password_here@localhost:3306/digital_invoice_db

# Server Configuration
NODE_ENV=production
PORT=3001

# Session Configuration
SESSION_SECRET=your_very_secure_session_secret_here_minimum_32_characters

# PayPal Configuration (Optional)
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
PAYPAL_MODE=sandbox  # or 'live' for production

# Telegram Bot Configuration (Optional)
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id

# Email Configuration (Optional - can be configured via admin panel)
SMTP_HOST=your_smtp_host
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=your_smtp_username
SMTP_PASS=your_smtp_password
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=Your Business Name

# Security Configuration
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_secure_admin_password

# File Upload Configuration
MAX_FILE_SIZE=10485760  # 10MB in bytes
UPLOAD_PATH=./uploads

# Application Configuration
SITE_NAME=Your Business Name
SITE_DESCRIPTION=Your business description
SITE_URL=https://yourdomain.com
```

#### **5. Install Dependencies & Build**
```bash
cd /var/www/digital-invoice

# Install dependencies
npm install --production

# Build the application
npm run build

# Setup database
npm run db:setup-mysql
```

#### **6. CloudPanel Site Configuration**

**Create New Site in CloudPanel:**
1. Go to CloudPanel → Sites → Add Site
2. Choose "Node.js" as site type
3. Set domain name (e.g., `invoice.yourdomain.com`)
4. Set document root: `/var/www/digital-invoice`
5. Set Node.js version: 18+

**Configure Reverse Proxy:**
1. Go to Site → Reverse Proxy
2. Add new proxy rule:
   - **Path**: `/`
   - **Proxy Pass**: `http://127.0.0.1:3001`
   - **Enable WebSocket**: Yes

**SSL Certificate:**
1. Go to Site → SSL/TLS
2. Enable "Let's Encrypt" for automatic SSL

#### **7. Process Management (PM2)**
```bash
# Install PM2 globally
sudo npm install -g pm2

# Create PM2 ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'digital-invoice',
    script: './dist/index.js',
    cwd: '/var/www/digital-invoice',
    env: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    instances: 1,
    exec_mode: 'fork',
    watch: false,
    max_memory_restart: '1G',
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
EOF

# Create logs directory
mkdir -p logs

# Start application with PM2
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Setup PM2 startup script
pm2 startup
# Follow the instructions provided by the command above
```

#### **8. Nginx Configuration (if needed)**
If using Nginx directly instead of CloudPanel's reverse proxy:
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    
    location / {
        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    location /uploads/ {
        alias /var/www/digital-invoice/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### **9. Security Hardening**
```bash
# Set proper file permissions
sudo chown -R www-data:www-data /var/www/digital-invoice
sudo chmod -R 755 /var/www/digital-invoice
sudo chmod 600 /var/www/digital-invoice/.env.production

# Setup firewall (if not already configured)
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable

# Setup log rotation
sudo cat > /etc/logrotate.d/digital-invoice << EOF
/var/www/digital-invoice/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        pm2 reload digital-invoice
    endscript
}
EOF
```

### **10. Monitoring & Maintenance**
```bash
# Check application status
pm2 status
pm2 logs digital-invoice

# Monitor system resources
pm2 monit

# Restart application
pm2 restart digital-invoice

# Update application (when needed)
cd /var/www/digital-invoice
git pull  # if using Git
npm install --production
npm run build
pm2 restart digital-invoice
```

## 📧 **EMAIL SYSTEM CONFIGURATION**

The application includes a sophisticated multi-SMTP email system:

### **SMTP Providers**
- Multiple SMTP providers can be configured
- Automatic failover between providers
- Provider-specific email templates
- Real-time provider health monitoring

### **Email Templates**
- HTML and plain text versions
- Dynamic content with variables
- Template categories (payment, trial, notifications)
- Gmail-optimized templates (no hidden images)

### **Email Features**
- Payment required notifications
- Trial subscription emails
- Order confirmations
- Admin notifications
- Telegram integration for alerts

## 🔧 **DEVELOPMENT SETUP**

### **Local Development**
```bash
# Clone repository
git clone <repository-url>
cd digital-invoice

# Install dependencies
npm install

# Setup environment
cp .env.example .env
# Edit .env with your configuration

# Setup database
npm run db:push

# Start development server
npm run dev
```

### **Available Scripts**
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run check` - TypeScript type checking
- `npm run db:push` - Push database schema
- `npm run db:setup-mysql` - Setup MySQL database

## 🔐 **SECURITY FEATURES**

- **Authentication**: Session-based with Passport.js
- **2FA Support**: TOTP-based two-factor authentication
- **IP Tracking**: Monitor login attempts and device access
- **Session Management**: Secure session handling with MySQL store
- **Input Validation**: Zod schema validation
- **CSRF Protection**: Built-in CSRF protection
- **Rate Limiting**: API rate limiting middleware
- **File Upload Security**: Secure file handling with type validation

## 📊 **ADMIN FEATURES**

- **Dashboard**: Overview of orders, revenue, and system status
- **Order Management**: View, edit, and process orders
- **Customer Management**: Customer database and communication
- **Email Templates**: Create and manage email templates
- **SMTP Configuration**: Configure multiple email providers
- **Payment Settings**: PayPal and custom payment configuration
- **System Monitoring**: Real-time system health monitoring
- **Data Export**: Export data to Excel/CSV formats
- **Backup System**: Automated database backups

## 🚀 **PRODUCTION DEPLOYMENT CHECKLIST**

- [ ] Server meets minimum requirements
- [ ] Database created and configured
- [ ] Environment variables set in `.env.production`
- [ ] Dependencies installed (`npm install --production`)
- [ ] Application built (`npm run build`)
- [ ] Database schema deployed (`npm run db:setup-mysql`)
- [ ] PM2 process manager configured
- [ ] Reverse proxy configured in CloudPanel
- [ ] SSL certificate installed
- [ ] Firewall configured
- [ ] Log rotation setup
- [ ] Monitoring configured
- [ ] Backup strategy implemented

## 📞 **SUPPORT & MAINTENANCE**

### **Log Files**
- Application logs: `/var/www/digital-invoice/logs/`
- PM2 logs: `pm2 logs digital-invoice`
- System logs: `/var/log/`

### **Common Issues**
1. **Database Connection**: Check DATABASE_URL and credentials
2. **Email Delivery**: Verify SMTP provider settings
3. **File Uploads**: Check upload directory permissions
4. **PayPal Integration**: Verify API credentials and webhook URLs

### **Performance Optimization**
- Enable Nginx gzip compression
- Configure proper caching headers
- Monitor database query performance
- Regular database maintenance and optimization
- Image optimization for uploads

---

**This application provides a complete solution for digital invoice management and IPTV subscription services with enterprise-grade features and CloudPanel.io hosting compatibility.**
