import React, { useState, useEffect } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import {
  Card, CardContent, CardDescription, CardHeader, CardTitle
} from '@/components/ui/card';
import {
  Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage
} from '@/components/ui/form';
import {
  AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent,
  AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle,
  AlertDialogTrigger
} from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import AdminLayout from '@/components/admin/AdminLayout';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { validateEmailDomain } from '@/lib/email-validator';
import { SiPaypal } from 'react-icons/si';
import { Contact, CheckCircle2, AlertTriangle, CreditCard, Code } from 'lucide-react';
import PayPalButtonEmbedSettings from '@/components/admin/PayPalButtonEmbedSettings';
import EmbedCodeSettings from '@/components/admin/EmbedCodeSettings';

// Type definitions
interface PaymentProvider {
  id: string;
  name: string;
  active: boolean;
  config: Record<string, string>;
}

interface PaymentConfig {
  providers: PaymentProvider[];
}

// Form schemas
const paypalSchema = z.object({
  clientId: z.string().min(1, 'Client ID is required'),
  clientSecret: z.string().min(1, 'Client Secret is required'),
  mode: z.enum(['sandbox', 'live']),
  webhookId: z.string().optional(),
  paypalEmail: z.string().email('Must be a valid email').min(1, 'PayPal email is required'),
  active: z.boolean().default(false)
});

// Schema for a single custom payment link
const customLinkItemSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, 'Name is required'),
  paymentLink: z.string().url('Must be a valid URL').min(1, 'Payment link is required'),
  buttonText: z.string().min(1, 'Button text is required'),
  successRedirectUrl: z.string().url('Must be a valid URL').optional().or(z.literal('')),
  active: z.boolean().default(true)
});

// Schema for the custom payment links configuration
const customLinkSchema = z.object({
  rotationMethod: z.enum(['round-robin', 'random']).default('round-robin'),
  active: z.boolean().default(false)
});

export default function PaymentSettingsPage() {
  const [isTestDialogOpen, setIsTestDialogOpen] = useState(false);
  const [isCustomLinkTestDialogOpen, setIsCustomLinkTestDialogOpen] = useState(false);
  const [isAddLinkDialogOpen, setIsAddLinkDialogOpen] = useState(false);
  const [isAddTrialLinkDialogOpen, setIsAddTrialLinkDialogOpen] = useState(false);
  const [isEditLinkDialogOpen, setIsEditLinkDialogOpen] = useState(false);
  const [testEmail, setTestEmail] = useState('');
  const [customLinkTestEmail, setCustomLinkTestEmail] = useState('');
  const [activeTab, setActiveTab] = useState('paypal'); // 'paypal', 'custom-link', 'paypal-button-embed', 'trial-custom-link', or 'trial-paypal-button-embed'
  const [customLinks, setCustomLinks] = useState<any[]>([]);
  const [selectedLinkId, setSelectedLinkId] = useState<string | null>(null);

  const { toast } = useToast();

  // Query to fetch payment configuration
  const { data: paymentConfig, isLoading, refetch } = useQuery<PaymentConfig>({
    queryKey: ['/api/admin/payment-config'],
    retry: false
  });

  // Default values for forms
  const paypalDefaults = paymentConfig?.providers.find(p => p.id === 'paypal')?.config || {
    clientId: '',
    clientSecret: '',
    mode: 'sandbox',
    webhookId: '',
    paypalEmail: ''
  };

  const customLinkProvider = paymentConfig?.providers.find(p => p.id === 'custom-link');
  const customLinkDefaults = customLinkProvider?.config || {
    links: [],
    rotationMethod: 'round-robin',
    lastUsedIndex: 0
  };

  const trialCustomLinkProvider = paymentConfig?.providers.find(p => p.id === 'trial-custom-link');
  const trialCustomLinkDefaults = trialCustomLinkProvider?.config || {
    links: [],
    rotationMethod: 'round-robin',
    lastUsedIndex: 0
  };

  const trialPaypalButtonEmbedProvider = paymentConfig?.providers.find(p => p.id === 'trial-paypal-button-embed');
  const trialPaypalButtonEmbedDefaults = trialPaypalButtonEmbedProvider?.config || {
    buttons: [],
    rotationMethod: 'round-robin',
    lastUsedIndex: 0
  };

  // State for trial custom links
  const [trialCustomLinks, setTrialCustomLinks] = useState<any[]>([]);

  // Update custom links when the config changes
  useEffect(() => {
    if (customLinkProvider && customLinkProvider.config) {
      const config = customLinkProvider.config as any;
      if (Array.isArray(config.links)) {
        setCustomLinks(config.links);
      } else if (config.paymentLink) {
        // Handle legacy format (single link)
        setCustomLinks([{
          id: 'link-1',
          name: 'Default Payment Link',
          paymentLink: config.paymentLink,
          buttonText: config.buttonText || 'Pay Now',
          successRedirectUrl: config.successRedirectUrl || '',
          active: true
        }]);
      } else {
        setCustomLinks([]);
      }
    }
  }, [customLinkProvider]);

  // Update trial custom links when the config changes
  useEffect(() => {
    if (trialCustomLinkProvider && trialCustomLinkProvider.config) {
      const config = trialCustomLinkProvider.config as any;
      if (Array.isArray(config.links)) {
        setTrialCustomLinks(config.links);
      } else if (config.paymentLink) {
        // Handle legacy format (single link)
        setTrialCustomLinks([{
          id: 'trial-link-1',
          name: 'Trial Payment Link',
          paymentLink: config.paymentLink,
          buttonText: config.buttonText || 'Start Trial',
          successRedirectUrl: config.successRedirectUrl || '',
          active: true
        }]);
      } else {
        setTrialCustomLinks([]);
      }
    }
  }, [trialCustomLinkProvider]);

  // PayPal form
  const paypalForm = useForm<z.infer<typeof paypalSchema>>({
    resolver: zodResolver(paypalSchema),
    defaultValues: {
      clientId: paypalDefaults.clientId || '',
      clientSecret: paypalDefaults.clientSecret || '',
      mode: (paypalDefaults.mode as 'sandbox' | 'live') || 'sandbox',
      webhookId: paypalDefaults.webhookId || '',
      paypalEmail: paypalDefaults.paypalEmail || '<EMAIL>',
      active: (paymentConfig?.providers.find(p => p.id === 'paypal')?.active) || false
    }
  });

  // Custom Link form for global settings
  const customLinkForm = useForm<z.infer<typeof customLinkSchema>>({
    resolver: zodResolver(customLinkSchema),
    defaultValues: {
      rotationMethod: (customLinkDefaults as any).rotationMethod || 'round-robin',
      active: (customLinkProvider?.active) || false
    }
  });

  // Trial Custom Link form for global settings
  const trialCustomLinkForm = useForm<z.infer<typeof customLinkSchema>>({
    resolver: zodResolver(customLinkSchema),
    defaultValues: {
      rotationMethod: (trialCustomLinkDefaults as any).rotationMethod || 'round-robin',
      active: (trialCustomLinkProvider?.active) || false
    }
  });

  // Form for adding/editing a single link
  const linkItemForm = useForm<z.infer<typeof customLinkItemSchema>>({
    resolver: zodResolver(customLinkItemSchema),
    defaultValues: {
      name: '',
      paymentLink: '',
      buttonText: 'Pay Now',
      successRedirectUrl: '',
      active: true
    }
  });

  // Form for adding/editing a single trial link
  const trialLinkItemForm = useForm<z.infer<typeof customLinkItemSchema>>({
    resolver: zodResolver(customLinkItemSchema),
    defaultValues: {
      name: '',
      paymentLink: '',
      buttonText: 'Start Trial',
      successRedirectUrl: '',
      active: true
    }
  });

  // Mutation to update PayPal config
  const updatePayPalMutation = useMutation({
    mutationFn: (data: z.infer<typeof paypalSchema>) =>
      apiRequest('/api/admin/payment-config/paypal', 'POST', data),
    onSuccess: () => {
      refetch();
      toast({
        title: "Settings updated",
        description: "PayPal configuration has been updated successfully."
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to update settings: ${error.message}`,
        variant: "destructive"
      });
    }
  });

  // Mutation to update Custom Link global config
  const updateCustomLinkMutation = useMutation({
    mutationFn: (data: z.infer<typeof customLinkSchema>) =>
      apiRequest('/api/admin/payment-config/custom-link', 'POST', data),
    onSuccess: () => {
      refetch();
      toast({
        title: "Settings updated",
        description: "Custom Payment Links configuration has been updated successfully."
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to update settings: ${error.message}`,
        variant: "destructive"
      });
    }
  });

  // Mutation to update Trial Custom Link global config
  const updateTrialCustomLinkMutation = useMutation({
    mutationFn: (data: z.infer<typeof customLinkSchema>) =>
      apiRequest('/api/admin/payment-config/trial-custom-link', 'POST', data),
    onSuccess: () => {
      refetch();
      toast({
        title: "Settings updated",
        description: "Trial Custom Payment Links configuration has been updated successfully."
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to update settings: ${error.message}`,
        variant: "destructive"
      });
    }
  });

  // Mutation to add a new custom payment link
  const addCustomLinkMutation = useMutation({
    mutationFn: (data: z.infer<typeof customLinkItemSchema>) =>
      apiRequest('/api/admin/payment-config/custom-link/add', 'POST', data),
    onSuccess: () => {
      refetch();
      setIsAddLinkDialogOpen(false);
      linkItemForm.reset();
      toast({
        title: "Link added",
        description: "Custom payment link has been added successfully."
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to add payment link: ${error.message}`,
        variant: "destructive"
      });
    }
  });

  // Mutation to add a new trial custom payment link
  const addTrialCustomLinkMutation = useMutation({
    mutationFn: async (data: z.infer<typeof customLinkItemSchema>) => {
      console.log('Adding trial custom payment link:', data);

      try {
        // Use the direct endpoint to add a trial payment link
        const response = await fetch('/api/direct-trial-link', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify(data)
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Error adding trial payment link:', errorText);
          throw new Error(errorText || response.statusText);
        }

        return await response.json();
      } catch (error) {
        console.error('Failed to add trial payment link:', error);
        throw error;
      }
    },
    onSuccess: (data) => {
      console.log('Successfully added trial custom payment link:', data);
      refetch();
      setIsAddTrialLinkDialogOpen(false);
      trialLinkItemForm.reset();
      toast({
        title: "Link added",
        description: "Trial custom payment link has been added successfully."
      });
    },
    onError: (error: Error) => {
      console.error('Failed to add trial custom payment link:', error);
      toast({
        title: "Error",
        description: `Failed to add trial payment link: ${error.message}`,
        variant: "destructive"
      });
    }
  });

  // Mutation to update a custom payment link
  const updateLinkMutation = useMutation({
    mutationFn: (data: { id: string, link: z.infer<typeof customLinkItemSchema> }) =>
      apiRequest(`/api/admin/payment-config/custom-link/${data.id}`, 'POST', data.link),
    onSuccess: () => {
      refetch();
      setIsEditLinkDialogOpen(false);
      setSelectedLinkId(null);
      toast({
        title: "Link updated",
        description: "Custom payment link has been updated successfully."
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to update payment link: ${error.message}`,
        variant: "destructive"
      });
    }
  });

  // Mutation to update a trial custom payment link
  const updateTrialLinkMutation = useMutation({
    mutationFn: (data: { id: string, link: z.infer<typeof customLinkItemSchema> }) =>
      apiRequest(`/api/admin/payment-config/trial-custom-link/${data.id}`, 'POST', data.link),
    onSuccess: () => {
      refetch();
      setIsEditLinkDialogOpen(false);
      setSelectedLinkId(null);
      toast({
        title: "Link updated",
        description: "Trial custom payment link has been updated successfully."
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to update trial payment link: ${error.message}`,
        variant: "destructive"
      });
    }
  });

  // Mutation to delete a custom payment link
  const deleteLinkMutation = useMutation({
    mutationFn: (id: string) =>
      apiRequest(`/api/admin/payment-config/custom-link/${id}`, 'DELETE'),
    onSuccess: () => {
      refetch();
      toast({
        title: "Link deleted",
        description: "Custom payment link has been deleted successfully."
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to delete payment link: ${error.message}`,
        variant: "destructive"
      });
    }
  });

  // Mutation to delete a trial custom payment link
  const deleteTrialLinkMutation = useMutation({
    mutationFn: (id: string) =>
      apiRequest(`/api/admin/payment-config/trial-custom-link/${id}`, 'DELETE'),
    onSuccess: () => {
      refetch();
      toast({
        title: "Link deleted",
        description: "Trial custom payment link has been deleted successfully."
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to delete trial payment link: ${error.message}`,
        variant: "destructive"
      });
    }
  });

  // Mutation to test PayPal connection
  const testConnectionMutation = useMutation({
    mutationFn: (data: { config: z.infer<typeof paypalSchema>, testEmail: string }) =>
      apiRequest('/api/admin/payment-config/test', 'POST', {
        provider: 'paypal',
        config: data.config,
        testEmail: data.testEmail
      }),
    onSuccess: (data) => {
      setIsTestDialogOpen(false);
      toast({
        title: "Connection successful",
        description: data.message || "PayPal API connection is working correctly."
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Connection failed",
        description: `Failed to connect to PayPal: ${error.message}`,
        variant: "destructive"
      });
    }
  });

  // Mutation to test Custom Link
  const testCustomLinkMutation = useMutation({
    mutationFn: (data: { linkId?: string, config?: any, testEmail: string }) =>
      apiRequest('/api/admin/payment-config/test', 'POST', {
        provider: 'custom-link',
        config: {
          linkId: data.linkId,
          ...data.config
        },
        testEmail: data.testEmail
      }),
    onSuccess: (data) => {
      setIsCustomLinkTestDialogOpen(false);
      toast({
        title: "Test successful",
        description: data.message || "Custom payment link is working correctly."
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Test failed",
        description: `Failed to test custom payment link: ${error.message}`,
        variant: "destructive"
      });
    }
  });

  // Mutation to test Trial Custom Link
  const testTrialCustomLinkMutation = useMutation({
    mutationFn: (data: { linkId?: string, config?: any, testEmail: string }) =>
      apiRequest('/api/admin/payment-config/test', 'POST', {
        provider: 'trial-custom-link',
        config: {
          linkId: data.linkId,
          ...data.config
        },
        testEmail: data.testEmail
      }),
    onSuccess: (data) => {
      setIsCustomLinkTestDialogOpen(false);
      toast({
        title: "Test successful",
        description: data.message || "Trial custom payment link is working correctly."
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Test failed",
        description: `Failed to test trial custom payment link: ${error.message}`,
        variant: "destructive"
      });
    }
  });

  const onPayPalSubmit = (data: z.infer<typeof paypalSchema>) => {
    updatePayPalMutation.mutate(data);
  };

  const onCustomLinkSubmit = (data: z.infer<typeof customLinkSchema>) => {
    updateCustomLinkMutation.mutate(data);
  };

  const onTrialCustomLinkSubmit = (data: z.infer<typeof customLinkSchema>) => {
    updateTrialCustomLinkMutation.mutate(data);
  };

  const onAddLinkSubmit = (data: z.infer<typeof customLinkItemSchema>) => {
    addCustomLinkMutation.mutate(data);
  };

  const onAddTrialLinkSubmit = (data: z.infer<typeof customLinkItemSchema>) => {
    console.log('onAddTrialLinkSubmit called with data:', data);

    // Use direct fetch instead of mutation
    fetch('/api/direct-trial-link', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      body: JSON.stringify(data)
    })
    .then(response => {
      if (!response.ok) {
        return response.text().then(text => {
          throw new Error(text || response.statusText);
        });
      }
      return response.json();
    })
    .then(data => {
      console.log('Successfully added trial custom payment link:', data);
      refetch();
      setIsAddTrialLinkDialogOpen(false);
      trialLinkItemForm.reset();
      toast({
        title: "Link added",
        description: "Trial custom payment link has been added successfully."
      });
    })
    .catch(error => {
      console.error('Failed to add trial custom payment link:', error);
      toast({
        title: "Error",
        description: `Failed to add trial payment link: ${error.message}`,
        variant: "destructive"
      });
    });
  };

  const onEditLinkSubmit = (data: z.infer<typeof customLinkItemSchema>) => {
    if (selectedLinkId) {
      // Check if this is a trial link or regular link
      if (selectedLinkId.startsWith('trial-')) {
        updateTrialLinkMutation.mutate({
          id: selectedLinkId,
          link: data
        });
      } else {
        updateLinkMutation.mutate({
          id: selectedLinkId,
          link: data
        });
      }
    }
  };

  const onDeleteLink = (id: string) => {
    if (confirm('Are you sure you want to delete this payment link?')) {
      deleteLinkMutation.mutate(id);
    }
  };

  const onDeleteTrialLink = (id: string) => {
    if (confirm('Are you sure you want to delete this trial payment link?')) {
      deleteTrialLinkMutation.mutate(id);
    }
  };

  const onTestConnection = () => {
    // Validate email domain
    const validation = validateEmailDomain(testEmail);
    if (!validation.isValid) {
      toast({
        title: "Invalid Email",
        description: validation.message || "Only email addresses from common providers are accepted",
        variant: "destructive"
      });
      return;
    }

    const config = paypalForm.getValues();
    testConnectionMutation.mutate({
      config,
      testEmail
    });
  };

  const onTestCustomLink = (linkId?: string) => {
    // Validate email domain
    const validation = validateEmailDomain(customLinkTestEmail);
    if (!validation.isValid) {
      toast({
        title: "Invalid Email",
        description: validation.message || "Only email addresses from common providers are accepted",
        variant: "destructive"
      });
      return;
    }

    if (linkId) {
      // Test a specific link
      const link = customLinks.find(link => link.id === linkId);
      if (link) {
        testCustomLinkMutation.mutate({
          linkId,
          testEmail: customLinkTestEmail
        });
      }
    } else {
      // Test using the current form values
      const link = linkItemForm.getValues();
      testCustomLinkMutation.mutate({
        config: link,
        testEmail: customLinkTestEmail
      });
    }
  };

  const onTestTrialCustomLink = (linkId?: string) => {
    // Validate email domain
    const validation = validateEmailDomain(customLinkTestEmail);
    if (!validation.isValid) {
      toast({
        title: "Invalid Email",
        description: validation.message || "Only email addresses from common providers are accepted",
        variant: "destructive"
      });
      return;
    }

    if (linkId) {
      // Test a specific link
      const link = trialCustomLinks.find(link => link.id === linkId);
      if (link) {
        testTrialCustomLinkMutation.mutate({
          linkId,
          testEmail: customLinkTestEmail
        });
      }
    } else {
      // Test using the current form values
      const link = trialLinkItemForm.getValues();
      testTrialCustomLinkMutation.mutate({
        config: link,
        testEmail: customLinkTestEmail
      });
    }
  };

  const openEditLinkDialog = (linkId: string) => {
    const link = customLinks.find(link => link.id === linkId);
    if (link) {
      setSelectedLinkId(linkId);
      linkItemForm.reset({
        name: link.name,
        paymentLink: link.paymentLink,
        buttonText: link.buttonText,
        successRedirectUrl: link.successRedirectUrl || '',
        active: link.active
      });
      setIsEditLinkDialogOpen(true);
    }
  };

  const openEditTrialLinkDialog = (linkId: string) => {
    const link = trialCustomLinks.find(link => link.id === linkId);
    if (link) {
      setSelectedLinkId(linkId);
      trialLinkItemForm.reset({
        name: link.name,
        paymentLink: link.paymentLink,
        buttonText: link.buttonText,
        successRedirectUrl: link.successRedirectUrl || '',
        active: link.active
      });
      setIsEditLinkDialogOpen(true);
    }
  };

  const toggleLinkActive = (linkId: string, active: boolean) => {
    const link = customLinks.find(link => link.id === linkId);
    if (link) {
      updateLinkMutation.mutate({
        id: linkId,
        link: {
          ...link,
          active
        }
      });
    }
  };

  const toggleTrialLinkActive = (linkId: string, active: boolean) => {
    const link = trialCustomLinks.find(link => link.id === linkId);
    if (link) {
      updateTrialLinkMutation.mutate({
        id: linkId,
        link: {
          ...link,
          active
        }
      });
    }
  };

  return (
    <AdminLayout>
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="text-2xl font-bold">Payment Settings</CardTitle>
          <CardDescription>
            Configure payment gateways for processing transactions
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center py-8">
              <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
            </div>
          ) : (
            <div className="space-y-6">
              <div className="flex space-x-4 border-b overflow-x-auto pb-1">
                <div className="flex-none">
                  <h3 className="text-sm font-semibold text-muted-foreground mb-2">Regular Payments</h3>
                  <div className="flex space-x-4">
                    <button
                      className={`px-4 py-2 font-medium ${activeTab === 'paypal' ? 'border-b-2 border-primary text-primary' : 'text-muted-foreground'}`}
                      onClick={() => setActiveTab('paypal')}
                    >
                      <div className="flex items-center space-x-2">
                        <SiPaypal className="h-5 w-5 text-[#0070ba]" />
                        <span>PayPal</span>
                      </div>
                    </button>
                    <button
                      className={`px-4 py-2 font-medium ${activeTab === 'custom-link' ? 'border-b-2 border-primary text-primary' : 'text-muted-foreground'}`}
                      onClick={() => setActiveTab('custom-link')}
                    >
                      <div className="flex items-center space-x-2">
                        <Contact className="h-5 w-5" />
                        <span>Custom Payment Link</span>
                      </div>
                    </button>
                    <button
                      className={`px-4 py-2 font-medium ${activeTab === 'paypal-button-embed' ? 'border-b-2 border-primary text-primary' : 'text-muted-foreground'}`}
                      onClick={() => setActiveTab('paypal-button-embed')}
                    >
                      <div className="flex items-center space-x-2">
                        <CreditCard className="h-5 w-5" />
                        <span>PayPal Button Embed</span>
                      </div>
                    </button>
                    <button
                      className={`px-4 py-2 font-medium ${activeTab === 'embed-codes' ? 'border-b-2 border-primary text-primary' : 'text-muted-foreground'}`}
                      onClick={() => setActiveTab('embed-codes')}
                    >
                      <div className="flex items-center space-x-2">
                        <Code className="h-5 w-5" />
                        <span>Embed Codes</span>
                      </div>
                    </button>
                  </div>
                </div>
                <div className="border-l mx-2"></div>
                <div className="flex-none">
                  <h3 className="text-sm font-semibold text-muted-foreground mb-2">Trial Payments</h3>
                  <div className="flex space-x-4">
                    <button
                      className={`px-4 py-2 font-medium ${activeTab === 'trial-custom-link' ? 'border-b-2 border-primary text-primary' : 'text-muted-foreground'}`}
                      onClick={() => setActiveTab('trial-custom-link')}
                    >
                      <div className="flex items-center space-x-2">
                        <Contact className="h-5 w-5 text-amber-500" />
                        <span>Trial Payment Link</span>
                      </div>
                    </button>
                    <button
                      className={`px-4 py-2 font-medium ${activeTab === 'trial-paypal-button-embed' ? 'border-b-2 border-primary text-primary' : 'text-muted-foreground'}`}
                      onClick={() => setActiveTab('trial-paypal-button-embed')}
                    >
                      <div className="flex items-center space-x-2">
                        <CreditCard className="h-5 w-5 text-amber-500" />
                        <span>Trial PayPal Button</span>
                      </div>
                    </button>
                  </div>
                </div>
              </div>

              {activeTab === 'paypal' && (
                <div className="space-y-6">
                  <div className="flex items-center space-x-2">
                    <SiPaypal className="h-8 w-8 text-[#0070ba]" />
                    <h2 className="text-xl font-bold">PayPal Configuration</h2>
                  </div>

                  <Form {...paypalForm}>
                    <form onSubmit={paypalForm.handleSubmit(onPayPalSubmit)} className="space-y-6">
                      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                        <p className="text-sm text-gray-500">
                          Configure your PayPal integration for invoice generation
                        </p>
                        <FormField
                          control={paypalForm.control}
                          name="active"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center space-x-3 space-y-0 mt-4 md:mt-0">
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                              <FormLabel className="font-normal">
                                {field.value ? 'Active' : 'Inactive'}
                              </FormLabel>
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <FormField
                          control={paypalForm.control}
                          name="clientId"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Client ID</FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="PayPal Client ID"
                                  {...field}
                                />
                              </FormControl>
                              <FormDescription>
                                Your PayPal application client ID
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={paypalForm.control}
                          name="clientSecret"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Client Secret</FormLabel>
                              <FormControl>
                                <Input
                                  type="password"
                                  placeholder="PayPal Client Secret"
                                  {...field}
                                />
                              </FormControl>
                              <FormDescription>
                                Your PayPal application client secret
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <FormField
                          control={paypalForm.control}
                          name="mode"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Environment</FormLabel>
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select environment" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="sandbox">Sandbox (Testing)</SelectItem>
                                  <SelectItem value="live">Live (Production)</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormDescription>
                                Select sandbox for testing, live for production
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={paypalForm.control}
                          name="webhookId"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Webhook ID <span className="text-gray-400">(Optional)</span></FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="PayPal Webhook ID"
                                  {...field}
                                />
                              </FormControl>
                              <FormDescription>
                                Webhook ID for payment notifications
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className="grid grid-cols-1 gap-6">
                        <FormField
                          control={paypalForm.control}
                          name="paypalEmail"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>PayPal Account Email</FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="Your PayPal account email"
                                  {...field}
                                />
                              </FormControl>
                              <FormDescription>
                                The email address associated with your PayPal account (required for sending invoices)
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className="flex justify-between">
                        <AlertDialog open={isTestDialogOpen} onOpenChange={setIsTestDialogOpen}>
                          <AlertDialogTrigger asChild>
                            <Button
                              type="button"
                              variant="outline"
                              onClick={() => setIsTestDialogOpen(true)}
                              disabled={!paypalForm.getValues('clientId') || !paypalForm.getValues('clientSecret')}
                            >
                              <Contact className="mr-2 h-4 w-4" /> Test Connection
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Test PayPal Connection</AlertDialogTitle>
                              <AlertDialogDescription>
                                This will test the connection to PayPal using the provided credentials.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <div className="py-4">
                              <label className="block text-sm font-medium mb-2">Test Email</label>
                              <Input
                                placeholder="Enter email address for test invoice"
                                value={testEmail}
                                onChange={(e) => setTestEmail(e.target.value)}
                                className="w-full"
                              />
                              <p className="text-sm text-muted-foreground mt-1">
                                A test invoice will be sent to this email address.
                              </p>
                            </div>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={onTestConnection}
                                disabled={!testEmail || testConnectionMutation.isPending}
                              >
                                {testConnectionMutation.isPending ? 'Testing...' : 'Test Connection'}
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>

                        <Button
                          type="submit"
                          disabled={updatePayPalMutation.isPending}
                        >
                          {updatePayPalMutation.isPending ? 'Saving...' : 'Save Settings'}
                        </Button>
                      </div>
                    </form>
                  </Form>

                  <div className="mt-8 border-t pt-6">
                    <h3 className="text-lg font-medium mb-2">API Connection Guide</h3>
                    <ol className="list-decimal list-inside space-y-2 text-sm text-gray-600">
                      <li>Log in to your <a href="https://developer.paypal.com" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">PayPal Developer account</a></li>
                      <li>Navigate to "My Apps & Credentials" and create a new REST API app</li>
                      <li>Copy the Client ID and Client Secret from your app's settings</li>
                      <li>Enter the email address associated with your PayPal account (this is required for sending invoices)</li>
                      <li>Start with "Sandbox" for testing before switching to "Live" for production</li>
                      <li>For webhooks, create one pointing to your site's API endpoint</li>
                    </ol>
                  </div>
                </div>
              )}

              {activeTab === 'custom-link' && (
                <div className="space-y-6">
                  <div className="flex items-center space-x-2">
                    <Contact className="h-8 w-8" />
                    <h2 className="text-xl font-bold">Custom Payment Links</h2>
                  </div>

                  {/* Global settings form */}
                  <Form {...customLinkForm}>
                    <form onSubmit={customLinkForm.handleSubmit(onCustomLinkSubmit)} className="space-y-6">
                      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                        <p className="text-sm text-gray-500">
                          Configure multiple payment links that will rotate between customers
                        </p>
                        <FormField
                          control={customLinkForm.control}
                          name="active"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center space-x-3 space-y-0 mt-4 md:mt-0">
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                              <FormLabel className="font-normal">
                                {field.value ? 'Active' : 'Inactive'}
                              </FormLabel>
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className="grid grid-cols-1 gap-6">
                        <FormField
                          control={customLinkForm.control}
                          name="rotationMethod"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Rotation Method</FormLabel>
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select rotation method" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="round-robin">Round Robin (Sequential)</SelectItem>
                                  <SelectItem value="random">Random</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormDescription>
                                How to rotate between multiple payment links
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <Button
                        type="submit"
                        disabled={updateCustomLinkMutation.isPending}
                      >
                        {updateCustomLinkMutation.isPending ? 'Saving...' : 'Save Settings'}
                      </Button>
                    </form>
                  </Form>

                  {/* Payment Links List */}
                  <div className="mt-8 border-t pt-6">
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-lg font-medium">Payment Links</h3>
                      <Button
                        onClick={() => {
                          linkItemForm.reset({
                            name: '',
                            paymentLink: '',
                            buttonText: 'Pay Now',
                            successRedirectUrl: '',
                            active: true
                          });
                          setIsAddLinkDialogOpen(true);
                        }}
                        size="sm"
                      >
                        Add New Link
                      </Button>
                    </div>

                    {customLinks.length === 0 ? (
                      <div className="text-center py-8 border rounded-md bg-gray-50">
                        <p className="text-gray-500">No payment links configured yet.</p>
                        <p className="text-sm text-gray-400 mt-1">Add your first payment link to get started.</p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {customLinks.map((link) => (
                          <div key={link.id} className="border rounded-md p-4 bg-white">
                            <div className="flex justify-between items-start">
                              <div>
                                <h4 className="font-medium flex items-center">
                                  {link.name}
                                  {link.active && (
                                    <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                      Active
                                    </span>
                                  )}
                                </h4>
                                <p className="text-sm text-gray-500 mt-1 truncate max-w-md">{link.paymentLink}</p>
                                <p className="text-xs text-gray-400 mt-1">Button: "{link.buttonText}"</p>
                              </div>
                              <div className="flex space-x-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    setCustomLinkTestEmail('');
                                    setSelectedLinkId(link.id);
                                    setIsCustomLinkTestDialogOpen(true);
                                  }}
                                >
                                  Test
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => openEditLinkDialog(link.id)}
                                >
                                  Edit
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => toggleLinkActive(link.id, !link.active)}
                                >
                                  {link.active ? 'Disable' : 'Enable'}
                                </Button>
                                <Button
                                  variant="destructive"
                                  size="sm"
                                  onClick={() => onDeleteLink(link.id)}
                                >
                                  Delete
                                </Button>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Add Link Dialog */}
                  <AlertDialog open={isAddLinkDialogOpen} onOpenChange={setIsAddLinkDialogOpen}>
                    <AlertDialogContent className="max-w-md">
                      <AlertDialogHeader>
                        <AlertDialogTitle>Add New Payment Link</AlertDialogTitle>
                        <AlertDialogDescription>
                          Configure a new payment link to add to the rotation.
                        </AlertDialogDescription>
                      </AlertDialogHeader>

                      <Form {...linkItemForm}>
                        <form onSubmit={linkItemForm.handleSubmit(onAddLinkSubmit)} className="space-y-4 py-4">
                          <FormField
                            control={linkItemForm.control}
                            name="name"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Name</FormLabel>
                                <FormControl>
                                  <Input placeholder="PayPal.me Link" {...field} />
                                </FormControl>
                                <FormDescription>
                                  A descriptive name for this payment link
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={linkItemForm.control}
                            name="paymentLink"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Payment Link URL</FormLabel>
                                <FormControl>
                                  <Input placeholder="https://example.com/pay" {...field} />
                                </FormControl>
                                <FormDescription>
                                  The URL where customers will be directed to make payment
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={linkItemForm.control}
                            name="buttonText"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Button Text</FormLabel>
                                <FormControl>
                                  <Input placeholder="Pay Now" {...field} />
                                </FormControl>
                                <FormDescription>
                                  Text to display on the payment button in the email
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={linkItemForm.control}
                            name="successRedirectUrl"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Success Redirect URL (Optional)</FormLabel>
                                <FormControl>
                                  <Input placeholder="https://example.com/thank-you" {...field} />
                                </FormControl>
                                <FormDescription>
                                  URL to redirect customers after successful payment
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={linkItemForm.control}
                            name="active"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  {field.value ? 'Active' : 'Inactive'}
                                </FormLabel>
                                <FormDescription className="ml-auto">
                                  Enable this payment link
                                </FormDescription>
                              </FormItem>
                            )}
                          />

                          <AlertDialogFooter className="mt-6">
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <Button
                              type="submit"
                              disabled={addCustomLinkMutation.isPending}
                            >
                              {addCustomLinkMutation.isPending ? 'Adding...' : 'Add Link'}
                            </Button>
                          </AlertDialogFooter>
                        </form>
                      </Form>
                    </AlertDialogContent>
                  </AlertDialog>

                  {/* Edit Link Dialog */}
                  <AlertDialog open={isEditLinkDialogOpen} onOpenChange={setIsEditLinkDialogOpen}>
                    <AlertDialogContent className="max-w-md">
                      <AlertDialogHeader>
                        <AlertDialogTitle>Edit Payment Link</AlertDialogTitle>
                        <AlertDialogDescription>
                          Update the configuration for this payment link.
                        </AlertDialogDescription>
                      </AlertDialogHeader>

                      <Form {...linkItemForm}>
                        <form onSubmit={linkItemForm.handleSubmit(onEditLinkSubmit)} className="space-y-4 py-4">
                          <FormField
                            control={linkItemForm.control}
                            name="name"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Name</FormLabel>
                                <FormControl>
                                  <Input placeholder="PayPal.me Link" {...field} />
                                </FormControl>
                                <FormDescription>
                                  A descriptive name for this payment link
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={linkItemForm.control}
                            name="paymentLink"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Payment Link URL</FormLabel>
                                <FormControl>
                                  <Input placeholder="https://example.com/pay" {...field} />
                                </FormControl>
                                <FormDescription>
                                  The URL where customers will be directed to make payment
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={linkItemForm.control}
                            name="buttonText"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Button Text</FormLabel>
                                <FormControl>
                                  <Input placeholder="Pay Now" {...field} />
                                </FormControl>
                                <FormDescription>
                                  Text to display on the payment button in the email
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={linkItemForm.control}
                            name="successRedirectUrl"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Success Redirect URL (Optional)</FormLabel>
                                <FormControl>
                                  <Input placeholder="https://example.com/thank-you" {...field} />
                                </FormControl>
                                <FormDescription>
                                  URL to redirect customers after successful payment
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={linkItemForm.control}
                            name="active"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  {field.value ? 'Active' : 'Inactive'}
                                </FormLabel>
                                <FormDescription className="ml-auto">
                                  Enable this payment link
                                </FormDescription>
                              </FormItem>
                            )}
                          />

                          <AlertDialogFooter className="mt-6">
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <Button
                              type="submit"
                              disabled={updateLinkMutation.isPending}
                            >
                              {updateLinkMutation.isPending ? 'Saving...' : 'Save Changes'}
                            </Button>
                          </AlertDialogFooter>
                        </form>
                      </Form>
                    </AlertDialogContent>
                  </AlertDialog>

                  {/* Test Link Dialog */}
                  <AlertDialog open={isCustomLinkTestDialogOpen} onOpenChange={setIsCustomLinkTestDialogOpen}>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Test Custom Payment Link</AlertDialogTitle>
                        <AlertDialogDescription>
                          Enter an email address to test the custom payment link. A test email will be sent to this address.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <div className="py-4">
                        <Input
                          type="email"
                          placeholder="Enter email address for test"
                          value={customLinkTestEmail}
                          onChange={(e) => setCustomLinkTestEmail(e.target.value)}
                          className="w-full"
                        />
                        <p className="text-sm text-muted-foreground mt-1">
                          A test email with the payment link will be sent to this address.
                        </p>

                      </div>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => {
                            if (activeTab === 'trial-custom-link') {
                              onTestTrialCustomLink(selectedLinkId || undefined);
                            } else {
                              onTestCustomLink(selectedLinkId || undefined);
                            }
                          }}
                          disabled={!customLinkTestEmail || testCustomLinkMutation.isPending || testTrialCustomLinkMutation.isPending}
                        >
                          {testCustomLinkMutation.isPending || testTrialCustomLinkMutation.isPending ? 'Testing...' : 'Test Payment Link'}
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              )}

              {activeTab === 'paypal-button-embed' && (
                <div className="space-y-6">
                  <div className="flex items-center space-x-2">
                    <CreditCard className="h-8 w-8" />
                    <h2 className="text-xl font-bold">PayPal Button Embed</h2>
                  </div>

                  <PayPalButtonEmbedSettings
                    paymentConfig={paymentConfig}
                    refetch={refetch}
                  />
                </div>
              )}

              {activeTab === 'trial-custom-link' && (
                <div className="space-y-6">
                  <div className="flex items-center space-x-2">
                    <Contact className="h-8 w-8 text-amber-500" />
                    <h2 className="text-xl font-bold">Trial Custom Payment Links</h2>
                  </div>

                  <div className="bg-amber-50 border border-amber-200 rounded-md p-4 mb-6">
                    <div className="flex items-start">
                      <AlertTriangle className="h-5 w-5 text-amber-500 mt-0.5 mr-2" />
                      <div>
                        <h3 className="font-medium text-amber-800">Trial Payment Configuration</h3>
                        <p className="text-sm text-amber-700 mt-1">
                          These payment links are specifically for trial subscriptions. They will be used when a checkout page is marked as a trial checkout.
                        </p>
                      </div>
                    </div>
                  </div>

                  <Form {...trialCustomLinkForm}>
                    <form onSubmit={trialCustomLinkForm.handleSubmit(onTrialCustomLinkSubmit)} className="space-y-6">
                      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                        <p className="text-sm text-gray-500">
                          Configure trial custom payment links for trial subscriptions
                        </p>
                        <FormField
                          control={trialCustomLinkForm.control}
                          name="active"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center space-x-3 space-y-0 mt-4 md:mt-0">
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                              <FormLabel className="font-normal">
                                {field.value ? 'Active' : 'Inactive'}
                              </FormLabel>
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className="grid grid-cols-1 gap-6">
                        <FormField
                          control={trialCustomLinkForm.control}
                          name="rotationMethod"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Rotation Method</FormLabel>
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select rotation method" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="round-robin">Round Robin (Sequential)</SelectItem>
                                  <SelectItem value="random">Random</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormDescription>
                                How to rotate between multiple payment links
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <Button
                        type="submit"
                        disabled={updateTrialCustomLinkMutation.isPending}
                      >
                        {updateTrialCustomLinkMutation.isPending ? 'Saving...' : 'Save Settings'}
                      </Button>
                    </form>
                  </Form>

                  {/* Trial Payment Links List */}
                  <div className="mt-8 border-t pt-6">
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-lg font-medium">Trial Payment Links</h3>
                      <Button
                        onClick={() => {
                          trialLinkItemForm.reset({
                            name: '',
                            paymentLink: '',
                            buttonText: 'Start Trial',
                            successRedirectUrl: '',
                            active: true
                          });
                          setIsAddTrialLinkDialogOpen(true);
                        }}
                        size="sm"
                      >
                        Add New Trial Link
                      </Button>
                    </div>

                    {trialCustomLinks.length === 0 ? (
                      <div className="text-center py-8 border rounded-md bg-gray-50">
                        <p className="text-gray-500">No trial payment links configured yet.</p>
                        <p className="text-sm text-gray-400 mt-1">Add your first trial payment link to get started.</p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {trialCustomLinks.map((link) => (
                          <div key={link.id} className="border rounded-md p-4 bg-white">
                            <div className="flex justify-between items-start">
                              <div>
                                <h4 className="font-medium flex items-center">
                                  {link.name}
                                  {link.active && (
                                    <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                      Active
                                    </span>
                                  )}
                                </h4>
                                <p className="text-sm text-gray-500 mt-1 truncate max-w-md">{link.paymentLink}</p>
                                <p className="text-xs text-gray-400 mt-1">Button: "{link.buttonText}"</p>
                              </div>
                              <div className="flex space-x-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    setCustomLinkTestEmail('');
                                    setSelectedLinkId(link.id);
                                    setIsCustomLinkTestDialogOpen(true);
                                  }}
                                >
                                  Test
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => openEditTrialLinkDialog(link.id)}
                                >
                                  Edit
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => toggleTrialLinkActive(link.id, !link.active)}
                                >
                                  {link.active ? 'Disable' : 'Enable'}
                                </Button>
                                <Button
                                  variant="destructive"
                                  size="sm"
                                  onClick={() => onDeleteTrialLink(link.id)}
                                >
                                  Delete
                                </Button>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              )}

              {activeTab === 'trial-paypal-button-embed' && (
                <div className="space-y-6">
                  <div className="flex items-center space-x-2">
                    <CreditCard className="h-8 w-8 text-amber-500" />
                    <h2 className="text-xl font-bold">Trial PayPal Button Embed</h2>
                  </div>

                  <div className="bg-amber-50 border border-amber-200 rounded-md p-4 mb-6">
                    <div className="flex items-start">
                      <AlertTriangle className="h-5 w-5 text-amber-500 mt-0.5 mr-2" />
                      <div>
                        <h3 className="font-medium text-amber-800">Trial Payment Configuration</h3>
                        <p className="text-sm text-amber-700 mt-1">
                          These PayPal buttons are specifically for trial subscriptions. They will be used when a checkout page is marked as a trial checkout.
                        </p>
                      </div>
                    </div>
                  </div>

                  <PayPalButtonEmbedSettings
                    paymentConfig={paymentConfig}
                    refetch={refetch}
                    providerId="trial-paypal-button-embed"
                    title="Trial PayPal Button Embed"
                    description="Configure trial PayPal button embeds for trial subscriptions"
                  />
                </div>
              )}

              {activeTab === 'embed-codes' && (
                <div className="space-y-6">
                  <div className="flex items-center space-x-2">
                    <Code className="h-8 w-8" />
                    <h2 className="text-xl font-bold">External Embed Codes</h2>
                  </div>

                  <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6">
                    <div className="flex items-start">
                      <AlertTriangle className="h-5 w-5 text-blue-500 mt-0.5 mr-2" />
                      <div>
                        <h3 className="font-medium text-blue-800">External Payment Platform Integration</h3>
                        <p className="text-sm text-blue-700 mt-1">
                          Configure embed codes from external payment platforms like BillGang.io, SellPass, and others.
                          Each embed code includes a head script and button HTML that will be injected into your checkout pages.
                        </p>
                      </div>
                    </div>
                  </div>

                  <EmbedCodeSettings />
                </div>
              )}

              {/* Add Trial Link Dialog */}
              <AlertDialog open={isAddTrialLinkDialogOpen} onOpenChange={setIsAddTrialLinkDialogOpen}>
                <AlertDialogContent className="max-w-md">
                  <AlertDialogHeader>
                    <AlertDialogTitle>Add New Trial Payment Link</AlertDialogTitle>
                    <AlertDialogDescription>
                      Configure a new trial payment link to add to the rotation.
                    </AlertDialogDescription>
                  </AlertDialogHeader>

                  <Form {...trialLinkItemForm}>
                    <form onSubmit={trialLinkItemForm.handleSubmit(onAddTrialLinkSubmit)} className="space-y-4 py-4">
                      <FormField
                        control={trialLinkItemForm.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Name</FormLabel>
                            <FormControl>
                              <Input placeholder="Trial Payment Link" {...field} />
                            </FormControl>
                            <FormDescription>
                              A descriptive name for this trial payment link
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={trialLinkItemForm.control}
                        name="paymentLink"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Payment Link URL</FormLabel>
                            <FormControl>
                              <Input placeholder="https://example.com/trial" {...field} />
                            </FormControl>
                            <FormDescription>
                              The URL where customers will be directed to start their trial
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={trialLinkItemForm.control}
                        name="buttonText"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Button Text</FormLabel>
                            <FormControl>
                              <Input placeholder="Start Trial" {...field} />
                            </FormControl>
                            <FormDescription>
                              Text to display on the trial button in the email
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={trialLinkItemForm.control}
                        name="successRedirectUrl"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Success Redirect URL (Optional)</FormLabel>
                            <FormControl>
                              <Input placeholder="https://example.com/trial-started" {...field} />
                            </FormControl>
                            <FormDescription>
                              URL to redirect customers after successful trial signup
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={trialLinkItemForm.control}
                        name="active"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <FormLabel className="font-normal">
                              {field.value ? 'Active' : 'Inactive'}
                            </FormLabel>
                            <FormDescription className="ml-auto">
                              Enable this trial payment link
                            </FormDescription>
                          </FormItem>
                        )}
                      />

                      <AlertDialogFooter className="mt-6">
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <Button
                          type="submit"
                          disabled={addTrialCustomLinkMutation.isPending}
                        >
                          {addTrialCustomLinkMutation.isPending ? 'Adding...' : 'Add Trial Link'}
                        </Button>
                      </AlertDialogFooter>
                    </form>
                  </Form>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          )}
        </CardContent>
      </Card>
    </AdminLayout>
  );
}