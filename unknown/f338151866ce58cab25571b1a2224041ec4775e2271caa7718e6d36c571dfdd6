import { db } from '../db';
import { appConfig, type InsertAppConfig, type AppConfig } from '@shared/schema';
import { eq } from 'drizzle-orm';

// Configuration interfaces
interface SecuritySettings {
  loginAlertsEnabled: boolean;
  ipRestrictionEnabled: boolean;
  telegram2FAEnabled: boolean;
}

interface EmailCredentials {
  fromEmail: string;
  fromName: string;
  host: string;
  port: string;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
}

interface EmailProvider {
  id: string;
  name: string;
  active: boolean;
  isDefault?: boolean;
  isBackup?: boolean;
  credentials: EmailCredentials;
}

interface EmailConfig {
  providers: EmailProvider[];
}

interface PaymentConfig {
  providers: any[];
}

/**
 * Database-backed configuration service
 */
export class DatabaseConfigService {
  private db: typeof db;

  constructor() {
    this.db = db;
  }

  /**
   * Get configuration value by key
   */
  async getConfig<T>(key: string, defaultValue: T): Promise<T> {
    try {
      const result = await this.db.select().from(appConfig).where(eq(appConfig.keyName, key));
      
      if (result.length === 0) {
        // If config doesn't exist, create it with default value
        await this.setConfig(key, defaultValue);
        return defaultValue;
      }

      return JSON.parse(result[0].value) as T;
    } catch (error) {
      console.error(`Error getting config ${key}:`, error);
      return defaultValue;
    }
  }

  /**
   * Set configuration value by key
   */
  async setConfig<T>(key: string, value: T): Promise<void> {
    try {
      const now = new Date().toISOString();
      const configData: InsertAppConfig = {
        keyName: key,
        value: JSON.stringify(value),
        updatedAt: now
      };

      // Check if config exists
      const existing = await this.db.select().from(appConfig).where(eq(appConfig.keyName, key));
      
      if (existing.length > 0) {
        // Update existing
        await this.db.update(appConfig)
          .set({ value: configData.value, updatedAt: configData.updatedAt })
          .where(eq(appConfig.keyName, key));
      } else {
        // Insert new
        await this.db.insert(appConfig).values(configData);
      }

      console.log(`✅ Config ${key} updated successfully`);
    } catch (error) {
      console.error(`Error setting config ${key}:`, error);
      throw error;
    }
  }

  /**
   * Get security settings
   */
  async getSecuritySettings(): Promise<SecuritySettings> {
    return this.getConfig('security', {
      loginAlertsEnabled: false,
      ipRestrictionEnabled: false,
      telegram2FAEnabled: false
    });
  }

  /**
   * Update security settings
   */
  async updateSecuritySettings(updates: Partial<SecuritySettings>): Promise<SecuritySettings> {
    const current = await this.getSecuritySettings();
    const updated = { ...current, ...updates };
    await this.setConfig('security', updated);
    return updated;
  }

  /**
   * Get email configuration
   */
  async getEmailConfig(): Promise<EmailConfig> {
    return this.getConfig('email', {
      providers: [
        {
          id: 'smtp-1',
          name: 'Primary SMTP',
          active: true,
          isDefault: true,
          isBackup: false,
          credentials: {
            host: 'smtp-relay.brevo.com',
            port: '587',
            secure: false,
            auth: {
              user: '<EMAIL>',
              pass: '3d8I9xFm1yMDYj7W'
            },
            fromEmail: '<EMAIL>',
            fromName: 'PayPal Invoicer'
          }
        }
      ]
    });
  }

  /**
   * Update email configuration
   */
  async updateEmailConfig(config: EmailConfig): Promise<void> {
    await this.setConfig('email', config);
  }

  /**
   * Get payment configuration
   */
  async getPaymentConfig(): Promise<PaymentConfig> {
    return this.getConfig('payment', {
      providers: [
        {
          id: 'paypal',
          name: 'PayPal',
          active: true,
          config: {
            clientId: '********************************************************************************',
            clientSecret: '********************************************************************************',
            mode: 'sandbox',
            webhookId: '',
            paypalEmail: '<EMAIL>'
          }
        },
        {
          id: 'custom-link',
          name: 'Custom Payment Links',
          active: true,
          config: {
            links: [
              {
                id: 'link-1',
                name: 'Default Payment Required',
                paymentLink: 'https://example.com/pay',
                buttonText: 'Complete Payment',
                successRedirectUrl: 'https://example.com/thank-you',
                active: true
              }
            ],
            rotationMethod: 'round-robin',
            lastUsedIndex: 0
          }
        }
      ]
    });
  }

  /**
   * Update payment configuration
   */
  async updatePaymentConfig(config: PaymentConfig): Promise<void> {
    await this.setConfig('payment', config);
  }

  /**
   * Initialize default configuration
   */
  async initializeDefaults(): Promise<void> {
    console.log('🔧 Initializing default configuration...');
    
    // Initialize security settings
    await this.getSecuritySettings();
    
    // Initialize email config
    await this.getEmailConfig();
    
    // Initialize payment config
    await this.getPaymentConfig();
    
    console.log('✅ Default configuration initialized');
  }

  /**
   * Export all configuration
   */
  async exportConfig(): Promise<Record<string, any>> {
    try {
      const allConfig = await this.db.select().from(appConfig);
      const exported: Record<string, any> = {};
      
      for (const config of allConfig) {
        exported[config.keyName] = JSON.parse(config.value);
      }
      
      return exported;
    } catch (error) {
      console.error('Error exporting config:', error);
      return {};
    }
  }

  /**
   * Import configuration
   */
  async importConfig(configData: Record<string, any>): Promise<void> {
    try {
      for (const [key, value] of Object.entries(configData)) {
        await this.setConfig(key, value);
      }
      console.log('✅ Configuration imported successfully');
    } catch (error) {
      console.error('Error importing config:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const databaseConfig = new DatabaseConfigService();
