import { defineConfig } from "drizzle-kit";
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

if (!process.env.DATABASE_URL) {
  throw new Error("DATABASE_URL environment variable is required in .env file.");
}

// Force MySQL dialect only - no SQLite or PostgreSQL
const dialect = "mysql";

console.log('🗄️ Using MySQL database only');
console.log('DATABASE_URL:', process.env.DATABASE_URL);

// Verify it's a MySQL connection
if (!process.env.DATABASE_URL.startsWith('mysql:')) {
  throw new Error("DATABASE_URL must be a MySQL connection string starting with 'mysql:'");
}

export default defineConfig({
  out: "./migrations",
  schema: "./shared/schema.ts",
  dialect: "mysql",
  dbCredentials: {
    url: process.env.DATABASE_URL,
  },
});
