{"version": "5", "dialect": "mysql", "id": "32ad85e0-0d89-4b8f-b8bf-1c76d7229f99", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"allowed_emails": {"name": "allowed_emails", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "smtp_provider": {"name": "smtp_provider", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_updated": {"name": "last_updated", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"allowed_emails_id": {"name": "allowed_emails_id", "columns": ["id"]}}, "uniqueConstraints": {"allowed_emails_email_unique": {"name": "allowed_emails_email_unique", "columns": ["email"]}}, "checkConstraint": {}}, "app_config": {"name": "app_config", "columns": {"key_name": {"name": "key_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"app_config_key_name": {"name": "app_config_key_name", "columns": ["key_name"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "custom_checkout_pages": {"name": "custom_checkout_pages", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "product_name": {"name": "product_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "product_description": {"name": "product_description", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "price": {"name": "price", "type": "decimal(10,2)", "primaryKey": false, "notNull": true, "autoincrement": false}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "payment_method": {"name": "payment_method", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "custom_payment_link_id": {"name": "custom_payment_link_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "paypal_button_id": {"name": "paypal_button_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "embed_code_id": {"name": "embed_code_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "require_allowed_email": {"name": "require_allowed_email", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "is_trial_checkout": {"name": "is_trial_checkout", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "confirmation_message": {"name": "confirmation_message", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "header_title": {"name": "header_title", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "footer_text": {"name": "footer_text", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "header_logo": {"name": "header_logo", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "footer_logo": {"name": "footer_logo", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "theme_mode": {"name": "theme_mode", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'light'"}, "use_referrer_masking": {"name": "use_referrer_masking", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "redirect_delay": {"name": "redirect_delay", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 2000}, "expires_at": {"name": "expires_at", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "views": {"name": "views", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "conversions": {"name": "conversions", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "created_at": {"name": "created_at", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"custom_checkout_pages_id": {"name": "custom_checkout_pages_id", "columns": ["id"]}}, "uniqueConstraints": {"custom_checkout_pages_slug_unique": {"name": "custom_checkout_pages_slug_unique", "columns": ["slug"]}}, "checkConstraint": {}}, "custom_invoices": {"name": "custom_invoices", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "invoice_number": {"name": "invoice_number", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "customer_name": {"name": "customer_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "customer_email": {"name": "customer_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "amount": {"name": "amount", "type": "decimal(10,2)", "primaryKey": false, "notNull": true, "autoincrement": false}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'USD'"}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "paypal_button_id": {"name": "paypal_button_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'pending'"}, "due_date": {"name": "due_date", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "view_count": {"name": "view_count", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "paid_at": {"name": "paid_at", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"custom_invoices_id": {"name": "custom_invoices_id", "columns": ["id"]}}, "uniqueConstraints": {"custom_invoices_invoice_number_unique": {"name": "custom_invoices_invoice_number_unique", "columns": ["invoice_number"]}}, "checkConstraint": {}}, "custom_payment_links": {"name": "custom_payment_links", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "button_text": {"name": "button_text", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'Pay Now'"}, "success_redirect_url": {"name": "success_redirect_url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "is_trial": {"name": "is_trial", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "created_at": {"name": "created_at", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"custom_payment_links_id": {"name": "custom_payment_links_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "email_templates": {"name": "email_templates", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "template_id": {"name": "template_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "subject": {"name": "subject", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "html_content": {"name": "html_content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "text_content": {"name": "text_content", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'general'"}, "is_default": {"name": "is_default", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "created_at": {"name": "created_at", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"email_templates_id": {"name": "email_templates_id", "columns": ["id"]}}, "uniqueConstraints": {"email_templates_template_id_unique": {"name": "email_templates_template_id_unique", "columns": ["template_id"]}}, "checkConstraint": {}}, "invoices": {"name": "invoices", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "customer_name": {"name": "customer_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "customer_email": {"name": "customer_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "product_id": {"name": "product_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "amount": {"name": "amount", "type": "decimal(10,2)", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'pending'"}, "paypal_invoice_id": {"name": "paypal_invoice_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "paypal_invoice_url": {"name": "paypal_invoice_url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_trial_order": {"name": "is_trial_order", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "has_upgraded": {"name": "has_upgraded", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "upgraded_at": {"name": "upgraded_at", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "custom_checkout_page_id": {"name": "custom_checkout_page_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "app_type": {"name": "app_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "mac_address": {"name": "mac_address", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"invoices_id": {"name": "invoices_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "paypal_buttons": {"name": "paypal_buttons", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "button_code": {"name": "button_code", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"paypal_buttons_id": {"name": "paypal_buttons_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "products": {"name": "products", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "price": {"name": "price", "type": "decimal(10,2)", "primaryKey": false, "notNull": true, "autoincrement": false}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"products_id": {"name": "products_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "sessions": {"name": "sessions", "columns": {"session_id": {"name": "session_id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires": {"name": "expires", "type": "int unsigned", "primaryKey": false, "notNull": true, "autoincrement": false}, "data": {"name": "data", "type": "mediumtext", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"sessions_session_id": {"name": "sessions_session_id", "columns": ["session_id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "smtp_providers": {"name": "smtp_providers", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "host": {"name": "host", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "port": {"name": "port", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true, "autoincrement": false}, "secure": {"name": "secure", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "username": {"name": "username", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "from_email": {"name": "from_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "from_name": {"name": "from_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "is_default": {"name": "is_default", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "is_backup": {"name": "is_backup", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "created_at": {"name": "created_at", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"smtp_providers_id": {"name": "smtp_providers_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "system_messages": {"name": "system_messages", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "message_id": {"name": "message_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_html": {"name": "is_html", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "created_at": {"name": "created_at", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"system_messages_id": {"name": "system_messages_id", "columns": ["id"]}}, "uniqueConstraints": {"system_messages_message_id_unique": {"name": "system_messages_message_id_unique", "columns": ["message_id"]}}, "checkConstraint": {}}, "telegram_pending_requests": {"name": "telegram_pending_requests", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"telegram_pending_requests_id": {"name": "telegram_pending_requests_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "telegram_processed_updates": {"name": "telegram_processed_updates", "columns": {"update_id": {"name": "update_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "processed_at": {"name": "processed_at", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"telegram_processed_updates_update_id": {"name": "telegram_processed_updates_update_id", "columns": ["update_id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "username": {"name": "username", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"users_id": {"name": "users_id", "columns": ["id"]}}, "uniqueConstraints": {"users_username_unique": {"name": "users_username_unique", "columns": ["username"]}}, "checkConstraint": {}}}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"tables": {}, "indexes": {}}}