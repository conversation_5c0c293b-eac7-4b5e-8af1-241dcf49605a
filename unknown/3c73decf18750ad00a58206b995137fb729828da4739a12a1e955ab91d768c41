import nodemailer from 'nodemailer';
import { Product, CheckoutData } from '../../shared/schema';
import { getEmailConfig } from '../config-storage';
import { db } from '../db';
import { smtpProviders } from '../../shared/schema';

// Get email config from database (not in-memory config)
async function getDatabaseEmailConfig() {
  try {
    // Get SMTP providers directly from database
    const providers = await db.select().from(smtpProviders);

    // Convert to the expected format
    const emailConfig = {
      providers: providers.map(p => ({
        id: p.id,
        name: p.name,
        active: <PERSON><PERSON><PERSON>(p.active),
        isDefault: <PERSON><PERSON><PERSON>(p.isDefault),
        credentials: {
          host: p.host,
          port: p.port,
          secure: Boolean(p.secure),
          auth: {
            user: p.username,
            pass: p.password
          },
          fromEmail: p.fromEmail,
          fromName: p.fromName
        }
      }))
    };

    console.log(`📤 Loaded ${emailConfig.providers.length} SMTP providers from database`);
    return emailConfig;
  } catch (error) {
    console.error('Error getting database email config:', error);
    // Fallback to in-memory config
    return getEmailConfig();
  }
}

/**
 * Create a configured nodemailer transporter based on settings
 * @param providerId Optional SMTP provider ID to use
 * @returns Nodemailer transporter
 */
export async function createTransporter(providerId?: string | null) {
  const emailConfig = await getDatabaseEmailConfig();

  // If providerId is specified and not 'default', try to find that specific provider
  if (providerId && providerId !== 'default') {
    console.log(`🔍 Looking for SMTP provider: ${providerId}`);
    console.log(`📋 Available providers:`, emailConfig.providers.map(p => `${p.name} (${p.id}) - Active: ${p.active}`));

    const provider = emailConfig.providers.find(p => p.id === providerId && p.active);
    if (provider) {
      console.log(`✅ Using specified SMTP provider: ${provider.name} (${provider.id})`);
      return nodemailer.createTransport({
        host: (provider.credentials as any).host,
        port: parseInt((provider.credentials as any).port),
        secure: (provider.credentials as any).secure,
        auth: {
          user: (provider.credentials as any).auth.user,
          pass: (provider.credentials as any).auth.pass
        }
      });
    } else {
      console.warn(`❌ Specified SMTP provider ${providerId} not found or not active, falling back to default`);
      console.log(`📋 Available providers:`, emailConfig.providers.map(p => `${p.name} (${p.id}) - Active: ${p.active}`));
    }
  }

  // Find the default provider
  const defaultProvider = emailConfig.providers.find(p => p.isDefault && p.active);
  if (defaultProvider) {
    console.log(`Using default SMTP provider: ${defaultProvider.name} (${defaultProvider.id})`);
    return nodemailer.createTransport({
      host: (defaultProvider.credentials as any).host,
      port: parseInt((defaultProvider.credentials as any).port),
      secure: (defaultProvider.credentials as any).secure,
      auth: {
        user: (defaultProvider.credentials as any).auth.user,
        pass: (defaultProvider.credentials as any).auth.pass
      }
    });
  }

  // If no default provider, use the first active provider
  const activeProvider = emailConfig.providers.find(p => p.active);
  if (activeProvider) {
    console.log(`Using active SMTP provider: ${activeProvider.name} (${activeProvider.id})`);
    return nodemailer.createTransport({
      host: (activeProvider.credentials as any).host,
      port: parseInt((activeProvider.credentials as any).port),
      secure: (activeProvider.credentials as any).secure,
      auth: {
        user: (activeProvider.credentials as any).auth.user,
        pass: (activeProvider.credentials as any).auth.pass
      }
    });
  }

  // Fallback to hardcoded transporter for testing
  console.warn('No active SMTP providers found, using hardcoded transporter');
  return nodemailer.createTransport({
    host: 'smtp-relay.brevo.com',
    port: 587,
    secure: false,
    auth: {
      user: '<EMAIL>',
      pass: '3d8I9xFm1yMDYj7W'
    }
  });
}

/**
 * Get the backup SMTP transporter if available
 * @returns Nodemailer transporter or null if no backup is available
 */
export function getBackupTransporter() {
  const emailConfig = getEmailConfig();

  // Find the backup provider
  const backupProvider = emailConfig.providers.find(p => p.isBackup && p.active);
  if (backupProvider) {
    console.log(`Using backup SMTP provider: ${backupProvider.name} (${backupProvider.id})`);
    return nodemailer.createTransport({
      host: (backupProvider.credentials as any).host,
      port: parseInt((backupProvider.credentials as any).port),
      secure: (backupProvider.credentials as any).secure,
      auth: {
        user: (backupProvider.credentials as any).auth.user,
        pass: (backupProvider.credentials as any).auth.pass
      }
    });
  }

  return null;
}

// Alias for backward compatibility
export const createEmailTransporter = createTransporter;

/**
 * Send a generic email with text and HTML content
 */
export async function sendEmail(
  emailData: {
    to: string;
    subject: string;
    text?: string;
    html?: string;
  },
  smtpSettings?: any,
  providerId?: string
): Promise<any> {
  try {
    // Create transporter with specific SMTP settings if provided
    let transporter;
    if (smtpSettings) {
      transporter = nodemailer.createTransporter(smtpSettings);
    } else {
      transporter = await createTransporter(providerId);
    }

    // Get credentials from the SMTP provider configuration
    const emailConfig = await getDatabaseEmailConfig();
    let credentials = {
      fromEmail: '<EMAIL>',
      fromName: 'PayPal Invoicer'
    };

    // If providerId is specified, get credentials from that provider
    if (providerId) {
      const provider = emailConfig.providers.find(p => p.id === providerId && p.active);
      if (provider && provider.credentials) {
        credentials = {
          fromEmail: (provider.credentials as any).fromEmail || credentials.fromEmail,
          fromName: (provider.credentials as any).fromName || credentials.fromName
        };
        console.log(`📤 Using SMTP provider credentials: ${provider.name} (${provider.id})`);
      }
    } else {
      // Use default provider credentials
      const defaultProvider = emailConfig.providers.find(p => p.isDefault && p.active) ||
                             emailConfig.providers.find(p => p.active);
      if (defaultProvider && defaultProvider.credentials) {
        credentials = {
          fromEmail: (defaultProvider.credentials as any).fromEmail || credentials.fromEmail,
          fromName: (defaultProvider.credentials as any).fromName || credentials.fromName
        };
        console.log(`📤 Using default SMTP provider credentials: ${defaultProvider.name} (${defaultProvider.id})`);
      }
    }

    const mailOptions = {
      from: `"${credentials.fromName}" <${credentials.fromEmail}>`,
      to: emailData.to,
      subject: emailData.subject,
      text: emailData.text,
      html: emailData.html
    };

    const info = await transporter.sendMail(mailOptions);
    console.log(`Email sent to ${emailData.to}. Message ID: ${info.messageId}`);
    return info;
  } catch (error) {
    console.error('Error sending email:', error);
    throw error;
  }
}

/**
 * Send a custom email with HTML content
 */
export async function sendCustomEmail(
  to: string,
  subject: string,
  htmlContent: string,
  providerId?: string | null
): Promise<any> {
  try {
    const transporter = await createTransporter(providerId);

    // Get credentials from the SMTP provider configuration
    const emailConfig = await getDatabaseEmailConfig();
    let credentials = {
      fromEmail: '<EMAIL>',
      fromName: 'PayPal Invoicer'
    };

    // If providerId is specified, get credentials from that provider
    if (providerId) {
      const provider = emailConfig.providers.find(p => p.id === providerId && p.active);
      if (provider && provider.credentials) {
        credentials = {
          fromEmail: (provider.credentials as any).fromEmail || credentials.fromEmail,
          fromName: (provider.credentials as any).fromName || credentials.fromName
        };
        console.log(`📤 Using SMTP provider credentials: ${provider.name} (${provider.id})`);
      }
    } else {
      // Use default provider credentials
      const defaultProvider = emailConfig.providers.find(p => p.isDefault && p.active) ||
                             emailConfig.providers.find(p => p.active);
      if (defaultProvider && defaultProvider.credentials) {
        credentials = {
          fromEmail: (defaultProvider.credentials as any).fromEmail || credentials.fromEmail,
          fromName: (defaultProvider.credentials as any).fromName || credentials.fromName
        };
        console.log(`📤 Using default SMTP provider credentials: ${defaultProvider.name} (${defaultProvider.id})`);
      }
    }

    const mailOptions = {
      from: `"${credentials.fromName}" <${credentials.fromEmail}>`,
      to,
      subject,
      html: htmlContent
    };

    const info = await transporter.sendMail(mailOptions);
    console.log(`Custom email sent to ${to}. Message ID: ${info.messageId}`);

    // No longer extracting username/password from email content
    return info;
  } catch (error) {
    console.error('Error sending custom email:', error);
    throw error;
  }
}

/**
 * Send a test email using the configured email provider
 * @param to Recipient email address
 * @param providerId Optional SMTP provider ID to use
 * @returns Boolean indicating success or failure
 */
export async function sendTestEmail(to: string, providerId?: string): Promise<boolean> {
  try {
    // Get the email configuration from database
    const emailConfig = await getDatabaseEmailConfig();

    // Get the provider to use
    let provider;

    if (providerId && providerId !== 'default') {
      provider = emailConfig.providers.find(p => p.id === providerId && p.active);
      if (!provider) {
        console.warn(`Specified SMTP provider ${providerId} not found or not active, falling back to default`);
      }
    }

    if (!provider) {
      provider = emailConfig.providers.find(p => p.isDefault && p.active) ||
                 emailConfig.providers.find(p => p.active);
    }

    if (!provider) {
      console.warn('No active SMTP provider found');
      return false;
    }

    const credentials = provider.credentials as any;
    const fromEmail = credentials.fromEmail;
    const fromName = credentials.fromName;

    try {
      // Try with the primary SMTP
      const transporter = await createTransporter(provider.id);
      if (!transporter) return false;

      const result = await transporter.sendMail({
        from: `"${fromName}" <${fromEmail}>`,
        to,
        subject: `Test Email from PayPal Invoice Generator (${provider.name})`,
        text: `This is a test email from your PayPal Invoice Generator app using the "${provider.name}" SMTP configuration. If you received this, your SMTP settings are working correctly.`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background-color: #0070BA; padding: 20px; text-align: center; color: white;">
              <h1 style="margin: 0;">Test Email</h1>
            </div>
            <div style="padding: 20px; border: 1px solid #e9e9e9; border-top: none;">
              <p>This is a test email from your PayPal Invoice Generator app using the <strong>"${provider.name}"</strong> SMTP configuration.</p>
              <p>If you're seeing this message, your SMTP settings are working correctly!</p>
              <p>You can now use this email configuration to send invoice notifications to your customers.</p>
              <div style="background-color: #f8f8f8; padding: 10px; border-left: 4px solid #0070BA; margin-top: 20px;">
                <p><strong>SMTP Configuration:</strong></p>
                <ul>
                  <li>Provider: ${provider.name}</li>
                  <li>Host: ${credentials.host}</li>
                  <li>From: ${fromName} &lt;${fromEmail}&gt;</li>
                </ul>
              </div>
            </div>
            <div style="background-color: #f5f5f5; padding: 15px; text-align: center; font-size: 12px; color: #666;">
              <p>© ${new Date().getFullYear()} PayPal Invoice Generator. All rights reserved.</p>
            </div>
          </div>
        `
      });

      console.log(`Test email sent to ${to} using ${provider.name} (${provider.id}). Message ID: ${result.messageId}`);
      return true;
    } catch (primaryError) {
      console.error(`Error sending test email via primary SMTP (${provider.id}):`, primaryError);

      // Try with the backup SMTP if available
      const backupTransporter = getBackupTransporter();
      if (backupTransporter) {
        try {
          const backupProvider = emailConfig.providers.find(p => p.isBackup && p.active);
          if (!backupProvider) throw new Error('Backup provider not found');

          const backupCredentials = backupProvider.credentials as any;

          const backupResult = await backupTransporter.sendMail({
            from: `"${backupCredentials.fromName}" <${backupCredentials.fromEmail}>`,
            to,
            subject: `Test Email from PayPal Invoice Generator (Backup: ${backupProvider.name})`,
            text: `This is a test email from your PayPal Invoice Generator app using the BACKUP "${backupProvider.name}" SMTP configuration. If you received this, your backup SMTP settings are working correctly.`,
            html: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <div style="background-color: #0070BA; padding: 20px; text-align: center; color: white;">
                  <h1 style="margin: 0;">Test Email (Backup)</h1>
                </div>
                <div style="padding: 20px; border: 1px solid #e9e9e9; border-top: none;">
                  <p>This is a test email from your PayPal Invoice Generator app using the <strong>BACKUP "${backupProvider.name}"</strong> SMTP configuration.</p>
                  <p>If you're seeing this message, your backup SMTP settings are working correctly!</p>
                  <p>The primary SMTP server (${provider.name}) failed, but the backup successfully delivered this email.</p>
                  <div style="background-color: #f8f8f8; padding: 10px; border-left: 4px solid #0070BA; margin-top: 20px;">
                    <p><strong>Backup SMTP Configuration:</strong></p>
                    <ul>
                      <li>Provider: ${backupProvider.name}</li>
                      <li>Host: ${backupCredentials.host}</li>
                      <li>From: ${backupCredentials.fromName} &lt;${backupCredentials.fromEmail}&gt;</li>
                    </ul>
                  </div>
                </div>
                <div style="background-color: #f5f5f5; padding: 15px; text-align: center; font-size: 12px; color: #666;">
                  <p>© ${new Date().getFullYear()} PayPal Invoice Generator. All rights reserved.</p>
                </div>
              </div>
            `
          });

          console.log(`Test email sent via backup SMTP ${backupProvider.name} (${backupProvider.id}):`, backupResult.messageId);
          return true;
        } catch (backupError) {
          console.error('Error sending test email via backup SMTP:', backupError);
          return false;
        }
      } else {
        return false;
      }
    }
  } catch (error) {
    console.error('Error sending test email:', error);
    return false;
  }
}

/**
 * Send invoice notification email to the customer
 * @param customerData Customer data
 * @param product Product data
 * @param invoiceUrl Invoice URL or object
 * @param paymentMethod Payment method
 * @param smtpProviderId Optional SMTP provider ID to use
 * @returns Boolean indicating success or failure
 */
export async function sendInvoiceEmail(
  customerData: CheckoutData,
  product: Product,
  invoiceUrl: string | any,
  paymentMethod: string = 'paypal',
  smtpProviderId?: string
): Promise<boolean> {
  try {
    // Get the email configuration from database
    const emailConfig = await getDatabaseEmailConfig();

    // Get the provider to use
    let provider;

    if (smtpProviderId && smtpProviderId !== 'default') {
      provider = emailConfig.providers.find(p => p.id === smtpProviderId && p.active);
      if (!provider) {
        console.warn(`Specified SMTP provider ${smtpProviderId} not found or not active, falling back to default`);
      }
    }

    if (!provider) {
      provider = emailConfig.providers.find(p => p.isDefault && p.active) ||
                 emailConfig.providers.find(p => p.active);
    }

    if (!provider) {
      console.warn('No active SMTP provider found');
      return false;
    }

    const credentials = provider.credentials as any;
    const fromEmail = credentials.fromEmail;
    const fromName = credentials.fromName;

    // Create the transporter
    const transporter = await createTransporter(provider.id);
    if (!transporter) {
      console.warn('Email transporter could not be created. Skipping email notification.');
      return false;
    }

    // Get the payment provider config to customize the email
    const paymentConfig = await getDatabaseEmailConfig();

    // Get button text for custom payment link
    let buttonText = 'Complete Payment';
    let paypalButtonHtml = '';

    if (paymentMethod === 'custom-link') {
      // Check if the invoice result has a button text (from the selected link)
      if (invoiceUrl && typeof invoiceUrl === 'object' && invoiceUrl.buttonText) {
        buttonText = invoiceUrl.buttonText;
        // Use the URL from the invoice result
        invoiceUrl = invoiceUrl.url;
      } else {
        // Fallback to default button text
        const customLinkProvider = (await import('../config-storage')).getPaymentConfig().providers.find(p => p.id === 'custom-link');
        if (customLinkProvider && customLinkProvider.config && customLinkProvider.config.links && customLinkProvider.config.links.length > 0) {
          // Use the first active link's button text
          const activeLink = customLinkProvider.config.links.find((link: any) => link.active);
          if (activeLink) {
            buttonText = activeLink.buttonText || buttonText;
          }
        }
      }
    } else if (paymentMethod === 'paypal-button-embed') {
      // Check if the invoice result has button HTML
      if (invoiceUrl && typeof invoiceUrl === 'object' && invoiceUrl.buttonHtml) {
        paypalButtonHtml = invoiceUrl.buttonHtml;
        // Set a placeholder URL for the invoice
        invoiceUrl = '#';
      }
    }

    // Use "Payment Required" for all payment methods
    const emailSubject = `Payment Required for ${product.name}`;
    const emailHeading = 'Payment Required';

    // Prepare email content
    const mailOptions = {
      from: `"${fromName}" <${fromEmail}>`,
      to: customerData.email,
      subject: emailSubject,
      text: `Hello ${customerData.fullName},\n\nThank you for your order! Your payment for ${product.name} is ready to be processed.\n\nTo complete your payment, please visit: ${invoiceUrl}\n\nIf you have any questions, please reply to this email.\n\nThank you,\n${fromName}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #333; margin: 0 0 10px 0; font-size: 24px;">${emailHeading}</h1>
            <p style="color: #666; font-size: 16px; margin: 0;">Complete your purchase for ${product.name}</p>
          </div>

          <div style="margin-bottom: 30px;">
            <h3 style="color: #333; margin: 0 0 15px 0; font-size: 18px;">Order Details:</h3>
            <table style="width: 100%; border-collapse: collapse;">
              <tr><td style="padding: 8px 0; color: #333;"><strong>Invoice Number:</strong></td><td style="padding: 8px 0; color: #666;">${paymentMethod === 'custom-link' ?
              (typeof invoiceUrl === 'string' && invoiceUrl.includes('paymentId=')) ? invoiceUrl.split('paymentId=')[1].split('&')[0] : `INV-${Date.now()}` :
              paymentMethod === 'paypal-button-embed' ?
              (typeof invoiceUrl === 'object' && invoiceUrl.id) ? invoiceUrl.id : `INV-${Date.now()}` :
              (typeof invoiceUrl === 'string' && invoiceUrl.includes('=')) ? invoiceUrl.split('=').pop() : `INV-${Date.now()}`}</td></tr>
              <tr><td style="padding: 8px 0; color: #333;"><strong>Date:</strong></td><td style="padding: 8px 0; color: #666;">${new Date().toLocaleDateString()}</td></tr>
              <tr><td style="padding: 8px 0; color: #333;"><strong>Product:</strong></td><td style="padding: 8px 0; color: #666;">${product.name}</td></tr>
              <tr><td style="padding: 8px 0; color: #333;"><strong>Description:</strong></td><td style="padding: 8px 0; color: #666;">${product.description || 'Premium service subscription'}</td></tr>
              <tr><td style="padding: 8px 0; color: #333;"><strong>Amount:</strong></td><td style="padding: 8px 0; color: #666;">$${product.price}</td></tr>
              <tr><td style="padding: 8px 0; color: #333;"><strong>Customer:</strong></td><td style="padding: 8px 0; color: #666;">${customerData.fullName}</td></tr>
            </table>
          </div>

          <div style="margin-bottom: 30px; padding: 15px; border: 1px solid #ddd;">
            <p style="margin: 0; color: #333; font-size: 14px;"><strong>Important:</strong> After payment confirmation, you will receive your subscription details by email within 8 hours during working hours. Most subscriptions are delivered within 3 hours of payment. Please check your spam folder if you cannot find the email.</p>
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <p style="color: #333; font-size: 16px; margin: 0 0 20px 0;">Click the link below to complete your payment:</p>
            <p style="margin: 20px 0;">
              <a href="${invoiceUrl}" style="display: inline-block; background-color: #007cba; color: white; padding: 15px 30px; text-decoration: none; font-weight: bold; font-size: 16px;">${buttonText}</a>
            </p>
            <p style="color: #666; font-size: 14px; margin: 20px 0 0 0;">Or copy and paste this link in your browser:<br>${invoiceUrl}</p>
          </div>

          <div style="margin-top: 30px; padding: 15px; border-top: 1px solid #ddd; text-align: center;">
            <p style="margin: 0 0 10px 0; color: #333;">If you have any questions, please reply to this email.</p>
            <p style="margin: 0; color: #333;"><strong>Thank you,<br>Support Team</strong></p>
          </div>
        </div>
      `
    };

    try {
      // Try with the primary SMTP
      const info = await transporter.sendMail(mailOptions);
      console.log(`Invoice notification email sent to ${customerData.email} using ${provider.name} (${provider.id}). Message ID: ${info.messageId}`);
      return true;
    } catch (primaryError) {
      console.error(`Error sending invoice email via primary SMTP (${provider.id}):`, primaryError);

      // Try with the backup SMTP if available
      const backupTransporter = getBackupTransporter();
      if (backupTransporter) {
        try {
          const backupInfo = await backupTransporter.sendMail(mailOptions);
          console.log(`Invoice notification email sent to ${customerData.email} via backup SMTP. Message ID: ${backupInfo.messageId}`);
          return true;
        } catch (backupError) {
          console.error('Error sending invoice email via backup SMTP:', backupError);
          return false;
        }
      } else {
        return false;
      }
    }
  } catch (error) {
    console.error('Error sending invoice email:', error);
    return false;
  }
}