CREATE TABLE `allowed_emails` (
	`id` int AUTO_INCREMENT NOT NULL,
	`email` varchar(255) NOT NULL,
	`notes` text,
	`smtp_provider` varchar(255),
	`last_updated` varchar(255),
	`created_at` varchar(255) NOT NULL,
	CONSTRAINT `allowed_emails_id` PRIMARY KEY(`id`),
	CONSTRAINT `allowed_emails_email_unique` UNIQUE(`email`)
);
--> statement-breakpoint
CREATE TABLE `app_config` (
	`key_name` varchar(255) NOT NULL,
	`value` text NOT NULL,
	`updated_at` varchar(255) NOT NULL,
	CONSTRAINT `app_config_key_name` PRIMARY KEY(`key_name`)
);
--> statement-breakpoint
CREATE TABLE `custom_checkout_pages` (
	`id` int AUTO_INCREMENT NOT NULL,
	`title` varchar(255) NOT NULL,
	`slug` varchar(255) NOT NULL,
	`product_name` varchar(255) NOT NULL,
	`product_description` text NOT NULL,
	`price` decimal(10,2) NOT NULL,
	`image_url` text,
	`payment_method` varchar(50) NOT NULL,
	`custom_payment_link_id` varchar(255),
	`paypal_button_id` varchar(255),
	`embed_code_id` varchar(255),
	`require_allowed_email` boolean NOT NULL DEFAULT false,
	`is_trial_checkout` boolean NOT NULL DEFAULT false,
	`confirmation_message` text,
	`header_title` text,
	`footer_text` text,
	`header_logo` text,
	`footer_logo` text,
	`theme_mode` varchar(20) NOT NULL DEFAULT 'light',
	`use_referrer_masking` boolean NOT NULL DEFAULT false,
	`redirect_delay` int NOT NULL DEFAULT 2000,
	`expires_at` varchar(255),
	`active` boolean NOT NULL DEFAULT true,
	`views` int NOT NULL DEFAULT 0,
	`conversions` int NOT NULL DEFAULT 0,
	`created_at` varchar(255) NOT NULL,
	`updated_at` varchar(255) NOT NULL,
	CONSTRAINT `custom_checkout_pages_id` PRIMARY KEY(`id`),
	CONSTRAINT `custom_checkout_pages_slug_unique` UNIQUE(`slug`)
);
--> statement-breakpoint
CREATE TABLE `custom_invoices` (
	`id` int AUTO_INCREMENT NOT NULL,
	`invoice_number` varchar(255) NOT NULL,
	`customer_name` varchar(255) NOT NULL,
	`customer_email` varchar(255) NOT NULL,
	`amount` decimal(10,2) NOT NULL,
	`currency` varchar(10) NOT NULL DEFAULT 'USD',
	`description` text NOT NULL,
	`paypal_button_id` int NOT NULL,
	`status` varchar(50) NOT NULL DEFAULT 'pending',
	`due_date` varchar(255),
	`view_count` int NOT NULL DEFAULT 0,
	`paid_at` varchar(255),
	`created_at` varchar(255) NOT NULL,
	`updated_at` varchar(255) NOT NULL,
	CONSTRAINT `custom_invoices_id` PRIMARY KEY(`id`),
	CONSTRAINT `custom_invoices_invoice_number_unique` UNIQUE(`invoice_number`)
);
--> statement-breakpoint
CREATE TABLE `custom_payment_links` (
	`id` varchar(255) NOT NULL,
	`name` varchar(255) NOT NULL,
	`url` text NOT NULL,
	`button_text` varchar(255) NOT NULL DEFAULT 'Pay Now',
	`success_redirect_url` text,
	`active` boolean NOT NULL DEFAULT true,
	`is_trial` boolean NOT NULL DEFAULT false,
	`created_at` varchar(255) NOT NULL,
	`updated_at` varchar(255) NOT NULL,
	CONSTRAINT `custom_payment_links_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `email_templates` (
	`id` int AUTO_INCREMENT NOT NULL,
	`template_id` varchar(255) NOT NULL,
	`name` varchar(255) NOT NULL,
	`description` text,
	`subject` varchar(255) NOT NULL,
	`html_content` text NOT NULL,
	`text_content` text,
	`content` text,
	`category` varchar(50) NOT NULL DEFAULT 'general',
	`is_default` boolean NOT NULL DEFAULT false,
	`created_at` varchar(255) NOT NULL,
	`updated_at` varchar(255) NOT NULL,
	CONSTRAINT `email_templates_id` PRIMARY KEY(`id`),
	CONSTRAINT `email_templates_template_id_unique` UNIQUE(`template_id`)
);
--> statement-breakpoint
CREATE TABLE `invoices` (
	`id` int AUTO_INCREMENT NOT NULL,
	`customer_name` varchar(255) NOT NULL,
	`customer_email` varchar(255) NOT NULL,
	`product_id` int NOT NULL,
	`amount` decimal(10,2) NOT NULL,
	`status` varchar(50) NOT NULL DEFAULT 'pending',
	`paypal_invoice_id` varchar(255),
	`paypal_invoice_url` text,
	`is_trial_order` boolean NOT NULL DEFAULT false,
	`has_upgraded` boolean NOT NULL DEFAULT false,
	`upgraded_at` varchar(255),
	`created_at` varchar(255) NOT NULL,
	`custom_checkout_page_id` int,
	`country` varchar(255),
	`app_type` varchar(255),
	`mac_address` varchar(255),
	CONSTRAINT `invoices_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `paypal_buttons` (
	`id` int AUTO_INCREMENT NOT NULL,
	`name` varchar(255) NOT NULL,
	`button_code` text NOT NULL,
	`description` text,
	`active` boolean NOT NULL DEFAULT true,
	`created_at` varchar(255) NOT NULL,
	`updated_at` varchar(255) NOT NULL,
	CONSTRAINT `paypal_buttons_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `products` (
	`id` int AUTO_INCREMENT NOT NULL,
	`name` varchar(255) NOT NULL,
	`description` text NOT NULL,
	`price` decimal(10,2) NOT NULL,
	`image_url` text NOT NULL,
	`active` boolean NOT NULL DEFAULT true,
	CONSTRAINT `products_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `sessions` (
	`session_id` varchar(128) NOT NULL,
	`expires` int unsigned NOT NULL,
	`data` mediumtext,
	CONSTRAINT `sessions_session_id` PRIMARY KEY(`session_id`)
);
--> statement-breakpoint
CREATE TABLE `smtp_providers` (
	`id` varchar(255) NOT NULL,
	`name` varchar(255) NOT NULL,
	`host` varchar(255) NOT NULL,
	`port` varchar(10) NOT NULL,
	`secure` boolean NOT NULL DEFAULT false,
	`username` varchar(255) NOT NULL,
	`password` varchar(255) NOT NULL,
	`from_email` varchar(255) NOT NULL,
	`from_name` varchar(255) NOT NULL,
	`active` boolean NOT NULL DEFAULT true,
	`is_default` boolean NOT NULL DEFAULT false,
	`is_backup` boolean NOT NULL DEFAULT false,
	`created_at` varchar(255) NOT NULL,
	`updated_at` varchar(255) NOT NULL,
	CONSTRAINT `smtp_providers_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `system_messages` (
	`id` int AUTO_INCREMENT NOT NULL,
	`message_id` varchar(255) NOT NULL,
	`category` varchar(50) NOT NULL,
	`name` varchar(255) NOT NULL,
	`description` text,
	`content` text NOT NULL,
	`is_html` boolean NOT NULL DEFAULT false,
	`created_at` varchar(255) NOT NULL,
	`updated_at` varchar(255) NOT NULL,
	CONSTRAINT `system_messages_id` PRIMARY KEY(`id`),
	CONSTRAINT `system_messages_message_id_unique` UNIQUE(`message_id`)
);
--> statement-breakpoint
CREATE TABLE `telegram_pending_requests` (
	`id` varchar(255) NOT NULL,
	`type` varchar(50) NOT NULL,
	`data` text NOT NULL,
	`expires_at` varchar(255) NOT NULL,
	`created_at` varchar(255) NOT NULL,
	CONSTRAINT `telegram_pending_requests_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `telegram_processed_updates` (
	`update_id` int NOT NULL,
	`processed_at` varchar(255) NOT NULL,
	CONSTRAINT `telegram_processed_updates_update_id` PRIMARY KEY(`update_id`)
);
--> statement-breakpoint
CREATE TABLE `users` (
	`id` int AUTO_INCREMENT NOT NULL,
	`username` varchar(255) NOT NULL,
	`password` varchar(255) NOT NULL,
	CONSTRAINT `users_id` PRIMARY KEY(`id`),
	CONSTRAINT `users_username_unique` UNIQUE(`username`)
);
