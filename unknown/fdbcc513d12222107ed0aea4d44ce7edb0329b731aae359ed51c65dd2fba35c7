import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, <PERSON><PERSON>Title, DialogTrigger } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Trash2, Search, Image as ImageIcon, Calendar, HardDrive } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface UploadedImage {
  filename: string;
  url: string;
  size: number;
  createdAt: string;
  modifiedAt: string;
}

interface ImageBrowserProps {
  onImageSelected: (url: string) => void;
  trigger?: React.ReactNode;
}

export default function ImageBrowser({ onImageSelected, trigger }: ImageBrowserProps) {
  const [images, setImages] = useState<UploadedImage[]>([]);
  const [filteredImages, setFilteredImages] = useState<UploadedImage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const { toast } = useToast();

  const fetchImages = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/upload/images');
      if (!response.ok) {
        throw new Error('Failed to fetch images');
      }
      const data = await response.json();
      setImages(data.images);
      setFilteredImages(data.images);
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to load images",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchImages();
    }
  }, [isOpen]);

  useEffect(() => {
    const filtered = images.filter(image =>
      image.filename.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredImages(filtered);
  }, [searchTerm, images]);

  const handleImageSelect = (url: string) => {
    onImageSelected(url);
    setIsOpen(false);
    toast({
      title: "Image selected",
      description: "The image has been selected successfully"
    });
  };

  const handleDeleteImage = async (filename: string) => {
    try {
      const response = await fetch(`/api/upload/image/${filename}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error('Failed to delete image');
      }

      // Remove the image from the list
      const updatedImages = images.filter(img => img.filename !== filename);
      setImages(updatedImages);
      setFilteredImages(updatedImages.filter(image =>
        image.filename.toLowerCase().includes(searchTerm.toLowerCase())
      ));

      toast({
        title: "Image deleted",
        description: "The image has been deleted successfully"
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete image",
        variant: "destructive"
      });
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" className="w-full">
            <ImageIcon className="h-4 w-4 mr-2" />
            Browse Existing Images
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle>Browse Uploaded Images</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Search */}
          <div className="flex items-center space-x-2">
            <Search className="h-4 w-4 text-gray-500" />
            <Input
              placeholder="Search images..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-1"
            />
          </div>

          {/* Images Grid */}
          <ScrollArea className="h-96">
            {isLoading ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                  <p className="mt-2 text-sm text-gray-500">Loading images...</p>
                </div>
              </div>
            ) : filteredImages.length === 0 ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-center">
                  <ImageIcon className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-500">
                    {searchTerm ? "No images found matching your search" : "No images uploaded yet"}
                  </p>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 p-2">
                {filteredImages.map((image) => (
                  <div
                    key={image.filename}
                    className="group relative border rounded-lg overflow-hidden hover:shadow-md transition-shadow cursor-pointer"
                    onClick={() => handleImageSelect(image.url)}
                  >
                    <div className="aspect-square overflow-hidden">
                      <img
                        src={image.url}
                        alt={image.filename}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform"
                      />
                    </div>
                    
                    {/* Image Info Overlay */}
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-60 transition-all duration-200 flex items-end">
                      <div className="p-2 text-white opacity-0 group-hover:opacity-100 transition-opacity w-full">
                        <p className="text-xs font-medium truncate">{image.filename}</p>
                        <div className="flex items-center justify-between text-xs mt-1">
                          <span className="flex items-center">
                            <HardDrive className="h-3 w-3 mr-1" />
                            {formatFileSize(image.size)}
                          </span>
                          <Button
                            variant="destructive"
                            size="sm"
                            className="h-6 w-6 p-0"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteImage(image.filename);
                            }}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </ScrollArea>

          {/* Summary */}
          {!isLoading && filteredImages.length > 0 && (
            <div className="flex items-center justify-between text-sm text-gray-500 pt-2 border-t">
              <span>
                {filteredImages.length} of {images.length} images
                {searchTerm && ` matching "${searchTerm}"`}
              </span>
              <span>Click an image to select it</span>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
