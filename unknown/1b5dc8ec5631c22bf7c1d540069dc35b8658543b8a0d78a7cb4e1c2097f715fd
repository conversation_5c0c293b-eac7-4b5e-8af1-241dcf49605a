import React from "react";
import { createRoot } from "react-dom/client";
import App from "./App";
import "./index.css";

console.log("🔍 main.tsx executing - React and App component are working!");
console.log("🔍 Issue identified: Homepage content/routing problem, NOT React problem");

const rootElement = document.getElementById("root");
console.log("🔍 Root element found:", !!rootElement);

if (rootElement) {
  console.log("✅ Creating React root...");
  const root = createRoot(rootElement);

  console.log("🚀 Rendering App...");
  root.render(<App />);
  console.log("✅ App rendered successfully");
} else {
  console.error("❌ Root element not found!");
}
