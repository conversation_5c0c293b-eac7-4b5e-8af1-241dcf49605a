import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Download, Printer } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface InvoiceViewerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  invoiceId: number | null;
}

export default function InvoiceViewer({ open, onOpenChange, invoiceId }: InvoiceViewerProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [invoiceHtml, setInvoiceHtml] = useState<string>('');
  const { toast } = useToast();

  useEffect(() => {
    if (open && invoiceId) {
      fetchInvoiceHtml();
    }
  }, [open, invoiceId]);

  const fetchInvoiceHtml = async () => {
    if (!invoiceId) return;

    setIsLoading(true);
    try {
      const response = await fetch(`/api/invoices/${invoiceId}/html`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch invoice');
      }
      
      const html = await response.text();
      setInvoiceHtml(html);
    } catch (error) {
      console.error('Error fetching invoice:', error);
      toast({
        title: "Error",
        description: "Failed to load invoice. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDownloadPdf = async () => {
    if (!invoiceId) return;

    try {
      // Create a link to download the PDF
      const link = document.createElement('a');
      link.href = `/api/invoices/${invoiceId}/pdf`;
      link.download = `invoice-${invoiceId}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      toast({
        title: "Success",
        description: "Invoice PDF download started.",
      });
    } catch (error) {
      console.error('Error downloading PDF:', error);
      toast({
        title: "Error",
        description: "Failed to download PDF. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handlePrint = () => {
    const iframe = document.getElementById('invoice-iframe') as HTMLIFrameElement;
    if (iframe && iframe.contentWindow) {
      iframe.contentWindow.print();
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>Invoice</DialogTitle>
          <DialogDescription>
            View and download invoice details
          </DialogDescription>
        </DialogHeader>

        <div className="flex justify-end space-x-2 mb-4">
          <Button
            variant="outline"
            onClick={handlePrint}
            disabled={isLoading}
          >
            <Printer className="mr-2 h-4 w-4" />
            Print
          </Button>
          <Button
            onClick={handleDownloadPdf}
            disabled={isLoading}
          >
            <Download className="mr-2 h-4 w-4" />
            Download PDF
          </Button>
        </div>

        <div className="flex-1 overflow-auto border rounded-md">
          {isLoading ? (
            <div className="flex justify-center items-center h-[500px]">
              <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
            </div>
          ) : (
            <iframe
              id="invoice-iframe"
              srcDoc={invoiceHtml}
              className="w-full h-[500px] border-0"
              title="Invoice"
            />
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
