import { defineConfig } from "drizzle-kit";
import * as dotenv from 'dotenv';

// Load production environment variables (override any existing env vars)
dotenv.config({ path: '.env.production', override: true });

if (!process.env.DATABASE_URL) {
  throw new Error("DATABASE_URL environment variable is required in .env.production file.");
}

// Determine database dialect based on the DATABASE_URL
const isSQLite = process.env.DATABASE_URL.startsWith('sqlite:');
const isMySQL = process.env.DATABASE_URL.startsWith('mysql:');

// Set the dialect based on the DATABASE_URL
let dialect = "postgresql"; // Default
if (isSQLite) {
  dialect = "sqlite";
} else if (isMySQL) {
  dialect = "mysql";
}

console.log('Using database:', dialect);
console.log('DATABASE_URL:', process.env.DATABASE_URL);

export default defineConfig({
  out: "./migrations",
  schema: "./shared/schema.ts",
  dialect: dialect,
  dbCredentials: {
    url: process.env.DATABASE_URL,
  },
});
