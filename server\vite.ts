import express, { type Express } from "express";
import fs from "fs";
import path from "path";
import { createServer as createViteServer, createLogger } from "vite";
import { type Server } from "http";
import { nanoid } from "nanoid";

// Default vite config for production
const viteConfig = {
  plugins: [],
  resolve: {
    alias: {
      "@": path.resolve(process.cwd(), "client", "src"),
      "@shared": path.resolve(process.cwd(), "shared"),
      "@assets": path.resolve(process.cwd(), "attached_assets"),
    },
  },
  root: path.resolve(process.cwd(), "client"),
  build: {
    outDir: path.resolve(process.cwd(), "dist/public"),
    emptyOutDir: true,
  },
};

const viteLogger = createLogger();

export function log(message: string, source = "express") {
  const formattedTime = new Date().toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    second: "2-digit",
    hour12: true,
  });

  console.log(`${formattedTime} [${source}] ${message}`);
}

export async function setupVite(app: Express, server: Server) {
  const serverOptions = {
    middlewareMode: true,
    hmr: { server },
    allowedHosts: true,
  };

  const vite = await createViteServer({
    ...viteConfig,
    configFile: false,
    customLogger: {
      ...viteLogger,
      error: (msg, options) => {
        viteLogger.error(msg, options);
        process.exit(1);
      },
    },
    server: serverOptions,
    appType: "custom",
  });

  app.use(vite.middlewares);
  app.use("*", async (req, res, next) => {
    // Skip API routes
    if (req.originalUrl.startsWith('/api')) {
      return next();
    }

    const url = req.originalUrl;

    try {
      const clientTemplate = path.resolve(
        import.meta.dirname,
        "..",
        "client",
        "index.html",
      );

      // always reload the index.html file from disk incase it changes
      let template = await fs.promises.readFile(clientTemplate, "utf-8");
      template = template.replace(
        `src="/src/main.tsx"`,
        `src="/src/main.tsx?v=${nanoid()}"`,
      );
      const page = await vite.transformIndexHtml(url, template);
      res.status(200).set({ "Content-Type": "text/html" }).end(page);
    } catch (e) {
      vite.ssrFixStacktrace(e as Error);
      next(e);
    }
  });
}

export function serveStatic(app: Express) {
  // Check both possible dist paths
  const distPath = path.resolve(import.meta.dirname, "public");
  const altDistPath = path.resolve(import.meta.dirname, "..", "dist", "public");

  let actualDistPath = distPath;

  // Check if the primary dist path exists
  if (!fs.existsSync(distPath)) {
    console.log(`Primary dist path not found: ${distPath}, checking alternative...`);

    // Check if the alternative dist path exists
    if (fs.existsSync(altDistPath)) {
      console.log(`Using alternative dist path: ${altDistPath}`);
      actualDistPath = altDistPath;
    } else {
      throw new Error(
        `Could not find any build directory. Tried:\n- ${distPath}\n- ${altDistPath}\nMake sure to build the client first`,
      );
    }
  }

  console.log(`Serving static files from: ${actualDistPath}`);

  // Serve static files
  app.use(express.static(actualDistPath));

  // Serve assets from the assets directory
  const assetsPath = path.join(actualDistPath, 'assets');
  if (fs.existsSync(assetsPath)) {
    console.log(`Serving assets from: ${assetsPath}`);
    app.use('/assets', express.static(assetsPath));
  }

  // fall through to index.html if the file doesn't exist
  app.use("*", (req, res) => {
    // Skip API routes
    if (req.originalUrl.startsWith('/api')) {
      return res.status(404).json({ message: "API endpoint not found" });
    }

    console.log(`Serving index.html for: ${req.originalUrl}`);

    // For all other routes, serve the index.html file
    res.sendFile(path.resolve(actualDistPath, "index.html"));
  });
}
