import axios from 'axios';
import { CheckoutData, Product } from "@shared/schema";
import { getPaymentConfig } from "../config-storage";

// Function to get PayPal access token
async function getPayPalAccessToken(clientId: string, clientSecret: string, mode: string): Promise<string> {
  const baseURL = mode === 'sandbox'
    ? 'https://api-m.sandbox.paypal.com'
    : 'https://api-m.paypal.com';

  try {
    const response = await axios({
      method: 'post',
      url: `${baseURL}/v1/oauth2/token`,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      auth: {
        username: clientId,
        password: clientSecret
      },
      data: 'grant_type=client_credentials'
    });

    return response.data.access_token;
  } catch (error) {
    console.error('Error getting PayPal access token:', error);
    throw error;
  }
}

// Function to validate an email for PayPal invoicing
// We'll only do basic validation to ensure it's a properly formatted email
async function isPayPalEmail(email: string, accessToken: string, mode: string): Promise<boolean> {
  console.log(`Validating email ${email} for PayPal invoicing`);

  // Basic email validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    console.log(`Email ${email} is not a valid email format`);
    return false;
  }

  // All properly formatted emails are considered valid for PayPal invoicing
  console.log(`Email ${email} is valid for PayPal invoicing`);
  return true;
}

// Function to create a PayPal invoice
export async function createPayPalInvoice(customerData: CheckoutData, product: Product): Promise<{ id: string, url: string, isSimulated?: boolean, error?: string, isDraft?: boolean, status?: string, noPayPalAccount?: boolean }> {
  try {
    // Get the PayPal settings from the configuration
    const paymentConfig = await getPaymentConfig();
    const paypalProvider = paymentConfig.providers.find((p: any) => p.id === 'paypal' && p.active);

    if (!paypalProvider) {
      console.warn('No active PayPal provider configured');
      throw new Error('PayPal is not configured or not active');
    }

    const { config } = paypalProvider;

    if (!config.clientId || !config.clientSecret) {
      throw new Error('PayPal client ID and client secret are required');
    }

    console.log('Generating PayPal invoice with settings:', {
      mode: config.mode,
      clientIdLength: config.clientId.length,
      hasSecret: Boolean(config.clientSecret),
      customer: customerData.email,
      invoicerEmail: config.paypalEmail || '<EMAIL>'
    });

    // Get access token
    const accessToken = await getPayPalAccessToken(config.clientId, config.clientSecret, config.mode);

    // Validate the customer's email for PayPal invoicing
    console.log('Validating customer email for PayPal invoicing:', customerData.email);
    const isValidEmail = await isPayPalEmail(customerData.email, accessToken, config.mode);

    if (!isValidEmail) {
      console.log('Customer email is not valid. Skipping invoice generation.');
      return {
        id: `NO-PAYPAL-${Date.now()}`,
        url: '#',
        noPayPalAccount: true,
        isSimulated: true,
        error: 'Invalid email format'
      };
    }

    console.log('Customer email is valid. Proceeding with PayPal invoice generation.');

    // Set API base URL based on mode
    const baseURL = config.mode === 'sandbox'
      ? 'https://api-m.sandbox.paypal.com'
      : 'https://api-m.paypal.com';

    // Format customer name
    const firstName = customerData.fullName.split(' ')[0] || customerData.fullName;
    const lastName = customerData.fullName.split(' ').slice(1).join(' ') || ' ';

    // Create invoice payload
    const invoicePayload = {
      detail: {
        invoice_number: `INV-${Date.now()}`,
        reference: `ORDER-${product.id}`,
        currency_code: "USD",
        note: "Thank you for your purchase! This is a gift invoice.",
        payment_term: {
          term_type: "DUE_ON_RECEIPT"
        }
      },
      invoicer: {
        name: {
          given_name: "PayPal",
          surname: "Invoicer"
        },
        // Use the email associated with the PayPal account
        email_address: config.paypalEmail || "<EMAIL>" // This should match your PayPal account email
      },
      primary_recipients: [
        {
          billing_info: {
            name: {
              given_name: firstName,
              surname: lastName
            },
            email_address: customerData.email
          }
        }
      ],
      items: [
        {
          name: product.name,
          description: product.description || 'Product purchase - Gift',
          quantity: "1",
          unit_amount: {
            currency_code: "USD",
            value: product.price.toString()
          }
        }
      ],
      configuration: {
        tax_calculated_after_discount: true,
        tax_inclusive: false,
        allow_tip: false
      }
    };

    try {
      // Create invoice
      console.log('Creating PayPal invoice with payload:', JSON.stringify(invoicePayload, null, 2));

      const createResponse = await axios({
        method: 'post',
        url: `${baseURL}/v2/invoicing/invoices`,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`
        },
        data: invoicePayload
      });

      console.log('Invoice creation response:', JSON.stringify(createResponse.data, null, 2));

      let invoiceId;

      // Handle different response formats from PayPal
      if (createResponse.data && createResponse.data.id) {
        // Standard response with ID directly in the response
        invoiceId = createResponse.data.id;
      } else if (createResponse.data && createResponse.data.href) {
        // Response with a link that contains the ID
        // Extract the ID from the href URL (e.g., "https://api.sandbox.paypal.com/v2/invoicing/invoices/INV2-L85J-YGYZ-YDKP-24FT")
        const urlParts = createResponse.data.href.split('/');
        invoiceId = urlParts[urlParts.length - 1];
      } else if (createResponse.data && createResponse.data.rel === 'self' && createResponse.data.href) {
        // Another format with rel and href
        const urlParts = createResponse.data.href.split('/');
        invoiceId = urlParts[urlParts.length - 1];
      } else {
        throw new Error('Failed to create invoice: No invoice ID returned');
      }
      console.log('Created invoice with ID:', invoiceId);

      // In Sandbox mode, we don't try to send the invoice automatically
      // because it often fails with USER_NOT_FOUND errors and the recipient_view_url doesn't work
      if (config.mode !== 'sandbox') {
        // Only attempt to send the invoice in production mode
        console.log('Sending invoice in production mode:', invoiceId);
        try {
          const sendResponse = await axios({
            method: 'post',
            url: `${baseURL}/v2/invoicing/invoices/${invoiceId}/send`,
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${accessToken}`
            },
            data: {
              send_to_recipient: true,
              send_to_invoicer: true
            }
          });

          console.log('Invoice send response:', JSON.stringify(sendResponse.data, null, 2));
        } catch (sendError) {
          console.error('Error sending invoice:', sendError.response?.data || sendError);
          console.log('Invoice created but not sent. It will remain in draft status.');
        }
      } else {
        console.log('Skipping automatic invoice sending in Sandbox mode to avoid USER_NOT_FOUND errors.');
        console.log('The invoice will remain in draft status. You can send it manually from your PayPal Sandbox dashboard.');
      }

      // Get invoice details to get the URL
      console.log('Getting invoice details:', invoiceId);
      const detailsResponse = await axios({
        method: 'get',
        url: `${baseURL}/v2/invoicing/invoices/${invoiceId}`,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`
        }
      });

      console.log('Invoice details response:', JSON.stringify(detailsResponse.data, null, 2));

      // Get the invoice URL - for draft invoices, we need to construct it manually
      let invoiceUrl;

      if (detailsResponse.data && detailsResponse.data.metadata && detailsResponse.data.metadata.recipient_view_url) {
        // If the invoice was sent, use the recipient_view_url
        invoiceUrl = detailsResponse.data.metadata.recipient_view_url;
        console.log('Using recipient view URL from sent invoice:', invoiceUrl);
      } else if (detailsResponse.data && detailsResponse.data.id) {
        // For draft invoices in Sandbox, we need to use a different approach
        if (config.mode === 'sandbox') {
          // In Sandbox, direct the user to the PayPal Sandbox dashboard to manage invoices
          invoiceUrl = 'https://www.sandbox.paypal.com/invoice/manage';
          console.log('Using PayPal Sandbox dashboard URL for draft invoice:', invoiceUrl);
        } else {
          // For production draft invoices, construct the URL manually
          invoiceUrl = `https://www.paypal.com/invoice/p/${detailsResponse.data.id}`;
          console.log('Constructed URL for draft invoice in production:', invoiceUrl);
        }
      } else {
        throw new Error('Failed to get invoice details');
      }

      console.log('Final invoice URL:', invoiceUrl);

      // Check if the invoice is in draft status
      const isDraft = detailsResponse.data &&
                     detailsResponse.data.status &&
                     detailsResponse.data.status === 'DRAFT';

      return {
        id: invoiceId,
        url: invoiceUrl,
        isDraft: isDraft,
        status: detailsResponse.data?.status || 'UNKNOWN'
      };
    } catch (error) {
      console.error('Error creating PayPal invoice:', error.response?.data || error);

      // Check if this is an API permissions issue
      if (error.response?.data?.name === 'PERMISSION_DENIED' ||
          error.response?.status === 403 ||
          error.response?.data?.message?.includes('permission')) {
        console.error('PayPal API permission denied. Make sure your account has Invoicing API access enabled.');
        throw new Error('PayPal Invoicing API access denied. Your PayPal account may not have Invoicing features enabled.');
      }

      // Check if this is a resource not found issue
      if (error.response?.data?.name === 'RESOURCE_NOT_FOUND' || error.response?.status === 404) {
        console.error('Resource not found. The Invoicing API endpoint may not be available for your account type.');
        throw new Error('PayPal Invoicing API not available. Your PayPal account may not support Invoicing features.');
      }

      throw error;
    }
  } catch (error) {
    console.error('Error generating PayPal invoice:', error);

    // Fallback to simulated invoice if there's an error
    console.log('Falling back to simulated invoice due to API error');

    // Create a more descriptive error message for the user
    let errorMessage = 'PayPal Invoicing API error';
    if (error.message) {
      errorMessage = error.message;
    }

    // Generate a simulated invoice ID and URL
    const invoiceId = `INV-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
    const encodedEmail = encodeURIComponent(customerData.email);

    // Get the payment config to determine the mode
    const paymentConfig = getPaymentConfig();
    const paypalProvider = paymentConfig.providers.find((p: any) => p.id === 'paypal' && p.active);
    const mode = paypalProvider?.config?.mode || 'sandbox';

    // Use sandbox or live URL based on the mode
    const baseUrl = mode === 'sandbox'
      ? 'https://www.sandbox.paypal.com'
      : 'https://www.paypal.com';

    const invoiceUrl = `${baseUrl}/invoice/p/${invoiceId}?recipient=${encodedEmail}&error=${encodeURIComponent(errorMessage)}`;

    console.log('Generated fallback invoice URL:', invoiceUrl);

    return {
      id: invoiceId,
      url: invoiceUrl,
      isSimulated: true,
      error: errorMessage
    };
  }
}

// Function to test PayPal connection
export async function testPayPalInvoiceConnection(config: any): Promise<boolean> {
  try {
    // Get access token to verify credentials
    await getPayPalAccessToken(config.clientId, config.clientSecret, config.mode);
    return true;
  } catch (error) {
    console.error('PayPal invoice connection test failed:', error);
    return false;
  }
}
